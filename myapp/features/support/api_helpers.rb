# API testing helpers for Cucumber scenarios
require 'jwt'

module ApiHelpers
  include ActionDispatch::Integration::Runner
  include ActionDispatch::Assertions

  def json_response
    @json_response ||= JSON.parse(@last_response.body)
  end

  def auth_headers(user = nil)
    return {} unless user

    token = generate_jwt_token(user)
    { 'Authorization' => "Bearer #{token}" }
  end

  def generate_jwt_token(user)
    payload = {
      user_id: user.id,
      exp: 24.hours.from_now.to_i
    }
    JWT.encode(payload, Rails.application.secret_key_base)
  end

  def make_api_request(method, path, params = {}, headers = {})
    default_headers = {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json'
    }

    # Create a new integration session for each request
    @integration_session = ActionDispatch::Integration::Session.new(Rails.application)

    case method.downcase
    when 'get'
      @integration_session.get path, params: params, headers: default_headers.merge(headers)
    when 'post'
      @integration_session.post path, params: params.to_json, headers: default_headers.merge(headers)
    when 'patch', 'put'
      @integration_session.patch path, params: params.to_json, headers: default_headers.merge(headers)
    when 'delete'
      @integration_session.delete path, params: params.to_json, headers: default_headers.merge(headers)
    end

    @last_response = @integration_session.response
    @json_response = nil # Reset cached JSON response
  end

  def expect_successful_response
    expect(@last_response.status).to be_between(200, 299)
  end

  def expect_error_response(status_code = nil)
    if status_code
      expect(@last_response.status).to eq(status_code)
    else
      expect(@last_response.status).to be >= 400
    end
  end

  def last_response
    @last_response
  end
end

World(ApiHelpers)
