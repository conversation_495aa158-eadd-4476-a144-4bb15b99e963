# Step definitions for appointment booking scenarios

# Background steps
Given('the system has verified doctors available') do
  @doctor_user = create(:user, :verified)
  @doctor = create(:doctor, :verified, user: @doctor_user, specialization: 'Gynecology')
  create(:doctor_availability, doctor: @doctor, day_of_week: Date.tomorrow.wday, is_available: true)
end

Given('I am a registered and verified patient') do
  @patient_user = create(:user, :verified)
  @patient = create(:patient, user: @patient_user)
end

# Authentication steps
Given('I am logged in as a patient') do
  step 'I am a registered and verified patient'
  @current_user = @patient_user
  @auth_headers = auth_headers(@current_user)
end

Given('I am not logged in') do
  @current_user = nil
  @auth_headers = {}
end

# Search steps
When('I search for doctors by specialization {string}') do |specialization|
  make_api_request('get', '/api/v1/search/doctors', { specialization: specialization }, @auth_headers)
end

When('I search for doctors with the following filters:') do |table|
  filters = table.rows_hash
  make_api_request('get', '/api/v1/search/doctors', filters, @auth_headers)
end

Then('I should see a list of available doctors') do
  expect_successful_response
  expect(json_response['doctors']).to be_an(Array)
  expect(json_response['doctors'].length).to be > 0
  @search_results = json_response['doctors']
end

Then('each doctor should have consultation fees displayed') do
  @search_results.each do |doctor|
    expect(doctor).to have_key('consultation_fee_day')
    expect(doctor).to have_key('consultation_fee_night')
    expect(doctor['consultation_fee_day']).to be > 0
  end
end

Then('I should see only doctors matching the criteria') do
  expect_successful_response
  @search_results = json_response['doctors']
  expect(@search_results).to be_an(Array)
end

Then('all doctors should have rating of {float} or higher') do |min_rating|
  @search_results.each do |doctor|
    expect(doctor['rating'].to_f).to be >= min_rating
  end
end

Then('all doctors should have consultation fees of {int} TL or less') do |max_fee|
  @search_results.each do |doctor|
    expect(doctor['consultation_fee_day'].to_f).to be <= max_fee
  end
end

# Doctor selection steps
When('I select a doctor from the search results') do
  @selected_doctor = @search_results.first
  @selected_doctor_id = @selected_doctor['id']
  make_api_request('get', "/api/v1/doctors/#{@selected_doctor_id}", {}, @auth_headers)
end

Then('I should see the doctor\'s detailed profile') do
  expect_successful_response
  @doctor_profile = json_response['doctor']
  expect(@doctor_profile).to have_key('specialization')
  expect(@doctor_profile).to have_key('years_experience')
  expect(@doctor_profile).to have_key('bio')
end

Then('I should see their available time slots') do
  make_api_request('get', "/api/v1/doctors/#{@selected_doctor_id}/availability", {}, @auth_headers)
  expect_successful_response
  @available_slots = json_response['available_slots']
  expect(@available_slots).to be_an(Array)
end

# Appointment booking steps
When('I select an available time slot for tomorrow') do
  tomorrow = Date.tomorrow
  @selected_slot = {
    scheduled_at: tomorrow.beginning_of_day + 10.hours # 10:00 AM tomorrow
  }
end

When('I provide appointment details:') do |table|
  @appointment_details = table.rows_hash
end

When('I confirm the appointment booking') do
  appointment_data = {
    doctor_id: @selected_doctor_id,
    scheduled_at: @selected_slot[:scheduled_at].iso8601,
    duration_minutes: 30,
    patient_notes: @appointment_details['complaint_summary'],
    appointment_type: @appointment_details['appointment_type']
  }

  make_api_request('post', '/api/v1/appointments', appointment_data, @auth_headers)
end

Then('the appointment should be created successfully') do
  expect_successful_response
  @created_appointment = json_response['appointment']
  expect(@created_appointment).to have_key('id')
  expect(@created_appointment['status']).to eq('scheduled')
end

Then('I should receive a booking confirmation') do
  expect(@created_appointment).to have_key('id')
  expect(@created_appointment['id']).not_to be_empty
end

Then('the doctor should be notified of the new appointment') do
  # This would typically check if a notification was created
  # For now, we'll just verify the appointment exists in the system
  expect(@created_appointment['doctor_id']).to eq(@selected_doctor_id)
end

# Appointment viewing steps
When('I check my appointments') do
  make_api_request('get', "/api/v1/patients/#{@patient.id}/appointments", {}, @auth_headers)
end

Then('I should see the newly booked appointment in my list') do
  expect_successful_response
  appointments = json_response['appointments']
  expect(appointments).to be_an(Array)
  
  booked_appointment = appointments.find { |apt| apt['id'] == @created_appointment['id'] }
  expect(booked_appointment).not_to be_nil
end

Then('the appointment status should be {string}') do |expected_status|
  appointments = json_response['appointments']
  booked_appointment = appointments.find { |apt| apt['id'] == @created_appointment['id'] }
  expect(booked_appointment['status']).to eq(expected_status)
end

# Error handling steps
When('I try to book an appointment') do
  appointment_data = {
    doctor_id: 1,
    scheduled_at: Date.tomorrow.beginning_of_day.+(10.hours).iso8601,
    duration_minutes: 30,
    appointment_type: 'consultation'
  }

  make_api_request('post', '/api/v1/appointments', appointment_data, @auth_headers)
end

Then('I should receive an authentication error') do
  expect_error_response(401)
  expect(json_response['error']).to include('authentication')
end

Then('the appointment should not be created') do
  expect_error_response
end

# Existing appointment steps
Given('I have an existing appointment') do
  @existing_appointment = create(:appointment, patient: @patient, doctor: @doctor)
end

When('I view my appointment details') do
  make_api_request('get', "/api/v1/appointments/#{@existing_appointment.id}", {}, @auth_headers)
end

Then('I should see all appointment information:') do |table|
  expect_successful_response
  appointment_data = json_response['appointment']
  
  table.raw.flatten.each do |field|
    expect(appointment_data).to have_key(field)
  end
end

Then('I should see options to cancel or reschedule if applicable') do
  appointment_data = json_response['appointment']
  if appointment_data['status'] == 'pending'
    expect(appointment_data).to have_key('can_cancel')
    expect(appointment_data).to have_key('can_reschedule')
  end
end
