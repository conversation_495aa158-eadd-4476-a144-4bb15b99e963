FactoryBot.define do
  factory :admin do
    association :user
    role { :admin }
    department { "Medical Administration" }
    permissions do
      {
        'users' => true,
        'doctors' => true,
        'content' => false,
        'billing' => false
      }
    end

    trait :super_admin do
      role { :super_admin }
      permissions do
        {
          'users' => true,
          'doctors' => true,
          'content' => true,
          'billing' => true
        }
      end
    end

    trait :moderator do
      role { :moderator }
      department { "Content Moderation" }
      permissions do
        {
          'users' => false,
          'doctors' => false,
          'content' => true,
          'billing' => false
        }
      end
    end
  end
end
