FactoryBot.define do
  factory :appointment do
    association :patient
    association :doctor
    scheduled_at { Date.tomorrow.beginning_of_day + 10.hours } # 10:00 AM tomorrow
    duration_minutes { 30 }
    appointment_type { :consultation }
    patient_notes { "Regular checkup" }
    status { :scheduled }
    consultation_fee { 200.0 }
    skip_future_validation { true }
    skip_availability_validation { true }

    trait :confirmed do
      status { :confirmed }
    end

    trait :completed do
      status { :completed }
    end

    trait :cancelled do
      status { :cancelled }
    end

    trait :emergency do
      appointment_type { :emergency }
      consultation_fee { 500.0 }
      patient_notes { "Urgent medical attention needed" }
    end

    trait :follow_up do
      appointment_type { :follow_up }
      patient_notes { "Follow-up visit" }
    end

    trait :today do
      scheduled_at { Date.current.beginning_of_day + 10.hours }
    end

    trait :next_week do
      scheduled_at { 1.week.from_now.beginning_of_day + 10.hours }
    end
  end
end
