FactoryBot.define do
  factory :patient do
    association :user
    emergency_contact_name { "<PERSON> Do<PERSON>" }
    emergency_contact_phone { "+905551234568" }
    blood_type { "A+" }
    pregnancy_count { 0 }
    birth_count { 0 }
    smoking_status { :non_smoker }
    alcohol_consumption { :never }

    trait :with_health_profile do
      after(:create) do |patient|
        create(:health_profile, patient: patient)
      end
    end

    trait :smoker do
      smoking_status { :current_smoker }
    end

    trait :pregnant do
      pregnancy_count { 1 }
      after(:create) do |patient|
        create(:pregnancy_tracker, patient: patient)
      end
    end

    trait :occasional_drinker do
      alcohol_consumption { :occasionally }
    end
  end
end
