FactoryBot.define do
  factory :user do
    sequence(:email) { |n| "user#{n}@example.com" }
    first_name { "<PERSON>" }
    last_name { "<PERSON><PERSON>" }
    phone_number { "+905551234567" }
    birth_date { 25.years.ago.to_date }
    password { "password123" }
    password_confirmation { "password123" }
    locale { "tr" }
    timezone { "Europe/Istanbul" }

    trait :verified do
      verified_at { 1.day.ago }
    end

    trait :unverified do
      verified_at { nil }
    end

    factory :patient_user do
      after(:create) do |user|
        create(:patient, user: user)
      end
    end

    factory :doctor_user do
      after(:create) do |user|
        create(:doctor, user: user)
      end
    end

    factory :admin_user do
      after(:create) do |user|
        create(:admin, user: user)
      end
    end
  end
end
