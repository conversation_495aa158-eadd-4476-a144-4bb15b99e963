FactoryBot.define do
  factory :doctor do
    association :user
    sequence(:license_number) { |n| "DOC#{n.to_s.rjust(6, '0')}" }
    specialization { "General Medicine" }
    sub_specializations { ["Internal Medicine"] }
    graduated_university { "Istanbul University Medical Faculty" }
    graduation_year { 2010 }
    current_institution { "City Hospital" }
    years_experience { 10 }
    consultation_fee_day { 200.0 }
    consultation_fee_night { 300.0 }
    consultation_fee_emergency { 500.0 }
    languages_spoken { ["Turkish", "English"] }
    bio { "Experienced doctor with 10+ years in practice" }
    verification_status { :pending }
    rating { 4.5 }
    total_consultations { 150 }

    trait :verified do
      verification_status { :verified }
    end

    trait :rejected do
      verification_status { :rejected }
    end

    trait :gynecologist do
      specialization { "Gynecology" }
      sub_specializations { ["Obstetrics", "Reproductive Health"] }
    end

    trait :cardiologist do
      specialization { "Cardiology" }
      sub_specializations { ["Interventional Cardiology"] }
    end

    trait :high_rated do
      rating { 4.8 }
      total_consultations { 500 }
    end

    trait :affordable do
      consultation_fee_day { 150.0 }
      consultation_fee_night { 200.0 }
    end

    trait :expensive do
      consultation_fee_day { 400.0 }
      consultation_fee_night { 600.0 }
    end
  end
end
