FactoryBot.define do
  factory :doctor_availability do
    association :doctor
    day_of_week { 1 } # Monday
    start_time { "09:00" }
    end_time { "17:00" }
    is_available { true }
    max_appointments { 8 }

    trait :monday do
      day_of_week { 1 }
    end

    trait :tuesday do
      day_of_week { 2 }
    end

    trait :wednesday do
      day_of_week { 3 }
    end

    trait :thursday do
      day_of_week { 4 }
    end

    trait :friday do
      day_of_week { 5 }
    end

    trait :saturday do
      day_of_week { 6 }
    end

    trait :sunday do
      day_of_week { 0 }
    end

    trait :unavailable do
      is_available { false }
    end

    trait :morning_only do
      start_time { "09:00" }
      end_time { "12:00" }
      max_appointments { 3 }
    end

    trait :afternoon_only do
      start_time { "13:00" }
      end_time { "17:00" }
      max_appointments { 4 }
    end

    trait :full_day do
      start_time { "08:00" }
      end_time { "18:00" }
      max_appointments { 10 }
    end
  end
end
