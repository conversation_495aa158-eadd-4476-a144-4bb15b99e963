class CreateCourseVideos < ActiveRecord::Migration[8.0]
  def change
    create_table :course_videos do |t|
      t.references :course_module, null: false, foreign_key: true
      t.string :title, null: false
      t.text :description
      t.string :video_url, null: false
      t.string :thumbnail_url
      t.integer :duration_seconds, null: false
      t.integer :sort_order, null: false
      t.boolean :is_published, default: false
      t.boolean :is_preview, default: false
      t.string :video_quality # HD, SD, etc.
      t.bigint :file_size_bytes
      t.json :subtitles # Array of subtitle objects
      t.json :video_metadata
      t.integer :view_count, default: 0

      t.timestamps
    end

    add_index :course_videos, [:course_module_id, :sort_order], unique: true
    add_index :course_videos, :is_published
    add_index :course_videos, :is_preview
  end
end
