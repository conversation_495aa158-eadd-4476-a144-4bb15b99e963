class CreateTierFeatures < ActiveRecord::Migration[8.0]
  def change
    create_table :tier_features do |t|
      t.references :membership_tier, null: false, foreign_key: true
      t.references :membership_feature, null: false, foreign_key: true
      t.integer :limit_value
      t.boolean :is_unlimited, default: false
      t.boolean :is_included, default: false

      t.timestamps
    end

    add_index :tier_features, [:membership_tier_id, :membership_feature_id],
              unique: true, name: 'index_tier_features_on_tier_and_feature'
    add_index :tier_features, :is_included
    add_index :tier_features, :is_unlimited
  end
end
