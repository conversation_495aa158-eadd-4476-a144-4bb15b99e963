class CreateOrders < ActiveRecord::Migration[8.0]
  def change
    create_table :orders do |t|
      t.references :user, null: false, foreign_key: true
      t.string :order_number, null: false
      t.integer :status, default: 0, null: false # pending, processing, completed, cancelled, refunded
      t.decimal :subtotal_amount, precision: 10, scale: 2, null: false
      t.decimal :tax_amount, precision: 10, scale: 2, default: 0.0
      t.decimal :discount_amount, precision: 10, scale: 2, default: 0.0
      t.decimal :total_amount, precision: 10, scale: 2, null: false
      t.string :currency, default: 'TRY', null: false
      t.string :order_type # membership, consultation, course, etc.
      t.json :billing_address
      t.string :discount_code
      t.text :notes
      t.datetime :confirmed_at
      t.datetime :shipped_at
      t.datetime :delivered_at
      t.datetime :cancelled_at
      t.string :cancellation_reason

      t.timestamps
    end

    add_index :orders, :order_number, unique: true
    add_index :orders, :status
    add_index :orders, :order_type
    add_index :orders, :confirmed_at
    add_index :orders, [:user_id, :status]
  end
end
