class CreateHealthMetrics < ActiveRecord::Migration[8.0]
  def change
    create_table :health_metrics do |t|
      t.references :patient, null: false, foreign_key: true
      t.references :trackable, polymorphic: true, null: true # Can be linked to cycle_tracker, pregnancy_tracker, etc.
      t.string :metric_type, null: false
      t.decimal :value, precision: 10, scale: 4, null: false
      t.string :unit, null: false
      t.datetime :recorded_at, null: false
      t.string :source, null: false # manual, device, app, etc.
      t.text :notes
      t.json :metadata # Additional data specific to metric type
      t.string :device_id # If recorded by a device
      t.decimal :reference_min, precision: 10, scale: 4
      t.decimal :reference_max, precision: 10, scale: 4
      t.boolean :is_abnormal, default: false
      t.string :trend # increasing, decreasing, stable
      t.integer :quality_score # Data quality score 1-10

      t.timestamps
    end

    add_index :health_metrics, :metric_type
    add_index :health_metrics, :recorded_at
    add_index :health_metrics, :source
    add_index :health_metrics, :is_abnormal
    add_index :health_metrics, [:patient_id, :metric_type, :recorded_at]
    add_index :health_metrics, [:trackable_type, :trackable_id]
  end
end
