class CreatePaymentMethods < ActiveRecord::Migration[8.0]
  def change
    create_table :payment_methods do |t|
      t.references :user, null: false, foreign_key: true
      t.string :payment_type, null: false # credit_card, debit_card, bank_account, digital_wallet
      t.string :provider, null: false # visa, mastercard, paypal, etc.
      t.string :token, null: false # Encrypted payment token
      t.string :last_four, null: false
      t.string :cardholder_name
      t.date :expires_at
      t.string :billing_address_line1
      t.string :billing_address_line2
      t.string :billing_city
      t.string :billing_state
      t.string :billing_postal_code
      t.string :billing_country, default: 'TR'
      t.boolean :is_default, default: false
      t.boolean :active, default: true
      t.datetime :verified_at
      t.json :metadata # Additional provider-specific data

      t.timestamps
    end

    add_index :payment_methods, :payment_type
    add_index :payment_methods, :provider
    add_index :payment_methods, :is_default
    add_index :payment_methods, :active
    add_index :payment_methods, [:user_id, :is_default]
  end
end
