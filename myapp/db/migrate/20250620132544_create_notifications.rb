class CreateNotifications < ActiveRecord::Migration[8.0]
  def change
    create_table :notifications do |t|
      t.references :user, null: false, foreign_key: true
      t.references :notifiable, polymorphic: true, null: true # Related object
      t.string :notification_type, null: false
      t.string :title, null: false
      t.text :message, null: false
      t.text :action_text
      t.string :action_url
      t.datetime :read_at
      t.datetime :sent_at, null: false
      t.integer :priority, default: 1, null: false # 1=low, 2=medium, 3=high, 4=urgent
      t.string :delivery_method # push, email, sms, in_app
      t.boolean :delivered, default: false
      t.datetime :delivered_at
      t.json :delivery_metadata
      t.datetime :expires_at
      t.string :icon
      t.string :color
      t.json :data # Additional notification data

      t.timestamps
    end

    add_index :notifications, :notification_type
    add_index :notifications, :read_at
    add_index :notifications, :sent_at
    add_index :notifications, :priority
    add_index :notifications, :delivered
    add_index :notifications, :expires_at
    add_index :notifications, [:user_id, :read_at]
    add_index :notifications, [:notifiable_type, :notifiable_id]
  end
end
