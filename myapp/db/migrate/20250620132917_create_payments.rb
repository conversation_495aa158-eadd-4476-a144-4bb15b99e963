class CreatePayments < ActiveRecord::Migration[8.0]
  def change
    create_table :payments do |t|
      t.references :order, null: false, foreign_key: true
      t.references :payment_method, null: false, foreign_key: true
      t.decimal :amount, precision: 10, scale: 2, null: false
      t.string :currency, default: 'TRY', null: false
      t.integer :status, default: 0, null: false # pending, processing, completed, failed, cancelled, refunded
      t.string :transaction_id
      t.string :gateway_transaction_id
      t.string :payment_gateway # stripe, paypal, iyzico, etc.
      t.datetime :processed_at
      t.datetime :failed_at
      t.datetime :refunded_at
      t.decimal :refunded_amount, precision: 10, scale: 2, default: 0.0
      t.text :failure_reason
      t.text :gateway_response
      t.json :gateway_metadata
      t.string :receipt_url
      t.boolean :is_test, default: false

      t.timestamps
    end

    add_index :payments, :transaction_id, unique: true
    add_index :payments, :gateway_transaction_id
    add_index :payments, :status
    add_index :payments, :processed_at
    add_index :payments, :payment_gateway
    add_index :payments, [:order_id, :status]
  end
end
