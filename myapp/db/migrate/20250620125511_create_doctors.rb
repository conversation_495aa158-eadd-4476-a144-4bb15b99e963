class CreateDoctors < ActiveRecord::Migration[8.0]
  def change
    create_table :doctors do |t|
      t.references :user, null: false, foreign_key: true
      t.string :license_number, null: false
      t.string :specialization, null: false
      t.text :sub_specializations
      t.string :graduated_university, null: false
      t.integer :graduation_year, null: false
      t.string :current_institution
      t.integer :years_experience, default: 0
      t.decimal :consultation_fee_day, precision: 8, scale: 2, null: false
      t.decimal :consultation_fee_night, precision: 8, scale: 2, null: false
      t.decimal :consultation_fee_emergency, precision: 8, scale: 2, null: false
      t.text :languages_spoken
      t.text :bio
      t.integer :verification_status, default: 0
      t.decimal :rating, precision: 3, scale: 2
      t.integer :total_consultations, default: 0

      t.timestamps
    end

    add_index :doctors, :license_number, unique: true
    add_index :doctors, :specialization
    add_index :doctors, :verification_status
    add_index :doctors, :rating
  end
end
