class CreateMembershipTiers < ActiveRecord::Migration[8.0]
  def change
    create_table :membership_tiers do |t|
      t.string :name, null: false
      t.string :slug, null: false
      t.text :description, null: false
      t.decimal :price, precision: 8, scale: 2, null: false, default: 0
      t.string :currency, null: false, default: 'TRY'
      t.integer :billing_cycle, null: false, default: 1
      t.integer :trial_days, default: 0
      t.integer :sort_order, null: false
      t.boolean :active, default: true

      t.timestamps
    end

    add_index :membership_tiers, :slug, unique: true
    add_index :membership_tiers, :active
    add_index :membership_tiers, :sort_order
    add_index :membership_tiers, :price
  end
end
