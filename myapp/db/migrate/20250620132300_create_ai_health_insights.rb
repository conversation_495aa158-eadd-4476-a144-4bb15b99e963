class CreateAiHealthInsights < ActiveRecord::Migration[8.0]
  def change
    create_table :ai_health_insights do |t|
      t.references :patient, null: false, foreign_key: true
      t.string :insight_type, null: false
      t.string :title, null: false
      t.text :content, null: false
      t.text :summary
      t.decimal :confidence_score, precision: 5, scale: 4, null: false
      t.datetime :generated_at, null: false
      t.integer :status, default: 0, null: false
      t.integer :priority, default: 1 # 1=low, 2=medium, 3=high, 4=critical
      t.json :data_sources # What data was used to generate this insight
      t.json :recommendations
      t.json :metadata
      t.string :ai_model_version
      t.boolean :reviewed_by_doctor, default: false
      t.references :reviewed_by, null: true, foreign_key: { to_table: :doctors }
      t.datetime :reviewed_at
      t.text :doctor_notes
      t.boolean :patient_acknowledged, default: false
      t.datetime :acknowledged_at
      t.datetime :expires_at

      t.timestamps
    end

    add_index :ai_health_insights, :insight_type
    add_index :ai_health_insights, :generated_at
    add_index :ai_health_insights, :status
    add_index :ai_health_insights, :priority
    add_index :ai_health_insights, :confidence_score
    add_index :ai_health_insights, :reviewed_by_doctor
    add_index :ai_health_insights, [:patient_id, :generated_at]
  end
end
