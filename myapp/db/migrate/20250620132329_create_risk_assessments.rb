class CreateRiskAssessments < ActiveRecord::Migration[8.0]
  def change
    create_table :risk_assessments do |t|
      t.references :patient, null: false, foreign_key: true
      t.references :assessed_by, null: true, foreign_key: { to_table: :doctors }
      t.string :assessment_type, null: false
      t.string :risk_level, null: false
      t.decimal :risk_score, precision: 5, scale: 2, null: false
      t.json :risk_factors # Array of risk factor objects
      t.json :protective_factors
      t.text :clinical_notes
      t.json :recommendations
      t.datetime :assessed_at, null: false
      t.datetime :valid_until
      t.boolean :requires_follow_up, default: false
      t.datetime :next_assessment_due
      t.string :assessment_method # questionnaire, clinical, ai_generated
      t.json :assessment_data # Raw assessment data
      t.integer :status, default: 0 # active, archived, superseded
      t.references :superseded_by, null: true, foreign_key: { to_table: :risk_assessments }

      t.timestamps
    end

    add_index :risk_assessments, :assessment_type
    add_index :risk_assessments, :risk_level
    add_index :risk_assessments, :assessed_at
    add_index :risk_assessments, :valid_until
    add_index :risk_assessments, :requires_follow_up
    add_index :risk_assessments, :next_assessment_due
    add_index :risk_assessments, [:patient_id, :assessment_type, :assessed_at]
  end
end
