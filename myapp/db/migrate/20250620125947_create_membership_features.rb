class CreateMembershipFeatures < ActiveRecord::Migration[8.0]
  def change
    create_table :membership_features do |t|
      t.string :name, null: false
      t.string :feature_key, null: false
      t.text :description, null: false
      t.integer :feature_type, null: false, default: 0
      t.string :category, null: false

      t.timestamps
    end

    add_index :membership_features, :feature_key, unique: true
    add_index :membership_features, :category
    add_index :membership_features, :feature_type
  end
end
