class CreateConsultations < ActiveRecord::Migration[8.0]
  def change
    create_table :consultations do |t|
      t.references :appointment, null: true, foreign_key: true # Can be null for direct consultations
      t.references :patient, null: false, foreign_key: true
      t.references :doctor, null: false, foreign_key: true
      t.string :consultation_type, null: false
      t.integer :status, default: 0, null: false
      t.datetime :started_at
      t.datetime :ended_at
      t.integer :duration_minutes
      t.text :chief_complaint, null: false
      t.text :history_of_present_illness
      t.text :physical_examination
      t.text :assessment
      t.text :plan
      t.text :follow_up_instructions
      t.text :additional_notes
      t.decimal :consultation_fee, precision: 8, scale: 2
      t.string :consultation_method # video, phone, in_person
      t.json :vital_signs
      t.json :symptoms
      t.boolean :prescription_issued, default: false
      t.datetime :next_follow_up_date

      t.timestamps
    end

    add_index :consultations, :started_at
    add_index :consultations, :status
    add_index :consultations, :consultation_type
    add_index :consultations, [:patient_id, :started_at]
    add_index :consultations, [:doctor_id, :started_at]
    add_index :consultations, :next_follow_up_date
  end
end
