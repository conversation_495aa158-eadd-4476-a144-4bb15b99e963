class CreateVideoProgresses < ActiveRecord::Migration[8.0]
  def change
    create_table :video_progresses do |t|
      t.references :course_video, null: false, foreign_key: true
      t.references :patient, null: false, foreign_key: true
      t.integer :watched_seconds, default: 0, null: false
      t.decimal :progress_percentage, precision: 5, scale: 2, default: 0.0
      t.boolean :completed, default: false
      t.datetime :first_watched_at
      t.datetime :last_watched_at
      t.datetime :completed_at
      t.integer :watch_count, default: 0
      t.json :watch_sessions # Array of watch session objects

      t.timestamps
    end

    add_index :video_progresses, [:course_video_id, :patient_id], unique: true
    add_index :video_progresses, :completed
    add_index :video_progresses, :last_watched_at
    add_index :video_progresses, :progress_percentage
  end
end
