class CreatePrescriptions < ActiveRecord::Migration[8.0]
  def change
    create_table :prescriptions do |t|
      t.references :consultation, null: false, foreign_key: true
      t.references :patient, null: false, foreign_key: true
      t.references :doctor, null: false, foreign_key: true
      t.string :prescription_number, null: false
      t.integer :status, default: 0, null: false
      t.datetime :issued_at, null: false
      t.datetime :expires_at, null: false
      t.text :instructions
      t.text :diagnosis
      t.text :additional_notes
      t.json :medications # Array of medication objects
      t.boolean :is_digital, default: true
      t.string :pharmacy_name
      t.string :pharmacy_address
      t.datetime :dispensed_at
      t.string :dispensed_by

      t.timestamps
    end

    add_index :prescriptions, :prescription_number, unique: true
    add_index :prescriptions, :issued_at
    add_index :prescriptions, :expires_at
    add_index :prescriptions, :status
    add_index :prescriptions, [:patient_id, :issued_at]
    add_index :prescriptions, [:doctor_id, :issued_at]
  end
end
