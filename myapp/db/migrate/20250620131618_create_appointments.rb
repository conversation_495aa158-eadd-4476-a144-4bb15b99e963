class CreateAppointments < ActiveRecord::Migration[8.0]
  def change
    create_table :appointments do |t|
      t.references :patient, null: false, foreign_key: true
      t.references :doctor, null: false, foreign_key: true
      t.string :appointment_type, null: false
      t.datetime :scheduled_at, null: false
      t.datetime :ends_at, null: false
      t.integer :status, default: 0, null: false
      t.integer :duration_minutes, default: 30, null: false
      t.decimal :consultation_fee, precision: 8, scale: 2, null: false
      t.text :patient_notes
      t.text :doctor_notes
      t.string :meeting_link
      t.string :meeting_id
      t.datetime :started_at
      t.datetime :ended_at
      t.string :cancellation_reason
      t.datetime :cancelled_at

      t.timestamps
    end

    add_index :appointments, :scheduled_at
    add_index :appointments, :status
    add_index :appointments, :appointment_type
    add_index :appointments, [:patient_id, :scheduled_at]
    add_index :appointments, [:doctor_id, :scheduled_at]
  end
end
