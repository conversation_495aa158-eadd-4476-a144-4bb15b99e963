class CreatePregnancyTrackers < ActiveRecord::Migration[8.0]
  def change
    create_table :pregnancy_trackers do |t|
      t.references :patient, null: false, foreign_key: true
      t.date :conception_date, null: false
      t.date :due_date, null: false
      t.date :last_menstrual_period
      t.integer :current_week, default: 0
      t.integer :current_trimester, default: 1
      t.decimal :pre_pregnancy_weight, precision: 5, scale: 2
      t.decimal :current_weight, precision: 5, scale: 2
      t.decimal :weight_gain, precision: 5, scale: 2, default: 0.0
      t.decimal :fundal_height, precision: 4, scale: 1
      t.integer :fetal_heart_rate
      t.integer :blood_pressure_systolic
      t.integer :blood_pressure_diastolic
      t.decimal :glucose_level, precision: 5, scale: 2
      t.decimal :iron_level, precision: 5, scale: 2
      t.json :symptoms # Array of pregnancy symptoms
      t.json :risk_factors
      t.text :notes
      t.boolean :active, default: true
      t.string :pregnancy_type, default: 'singleton' # singleton, twins, etc.
      t.datetime :delivery_date
      t.string :delivery_type # natural, cesarean, etc.

      t.timestamps
    end

    add_index :pregnancy_trackers, :due_date
    add_index :pregnancy_trackers, :current_week
    add_index :pregnancy_trackers, :current_trimester
    add_index :pregnancy_trackers, :active
    add_index :pregnancy_trackers, [:patient_id, :active]
  end
end
