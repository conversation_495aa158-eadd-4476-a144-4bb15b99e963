class CreateCourseEnrollments < ActiveRecord::Migration[8.0]
  def change
    create_table :course_enrollments do |t|
      t.references :course, null: false, foreign_key: true
      t.references :patient, null: false, foreign_key: true
      t.integer :status, default: 0, null: false
      t.datetime :enrolled_at, null: false
      t.datetime :completed_at
      t.datetime :last_accessed_at
      t.decimal :progress_percentage, precision: 5, scale: 2, default: 0.0
      t.integer :total_watch_time_seconds, default: 0
      t.decimal :rating, precision: 3, scale: 2
      t.text :review
      t.datetime :review_submitted_at
      t.boolean :certificate_issued, default: false
      t.datetime :certificate_issued_at

      t.timestamps
    end

    add_index :course_enrollments, [:course_id, :patient_id], unique: true
    add_index :course_enrollments, :status
    add_index :course_enrollments, :enrolled_at
    add_index :course_enrollments, :completed_at
    add_index :course_enrollments, :progress_percentage
  end
end
