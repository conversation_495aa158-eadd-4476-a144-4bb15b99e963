class CreateMedicalLicenses < ActiveRecord::Migration[8.0]
  def change
    create_table :medical_licenses do |t|
      t.references :doctor, null: false, foreign_key: true
      t.string :license_number, null: false
      t.string :issuing_authority, null: false
      t.date :issue_date, null: false
      t.date :expiry_date, null: false
      t.string :license_type, null: false
      t.integer :status, default: 0
      t.string :document_url

      t.timestamps
    end

    add_index :medical_licenses, :license_number, unique: true
    add_index :medical_licenses, :status
    add_index :medical_licenses, :expiry_date
    add_index :medical_licenses, :license_type
  end
end
