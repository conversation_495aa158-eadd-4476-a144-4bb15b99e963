class CreateCycleTrackers < ActiveRecord::Migration[8.0]
  def change
    create_table :cycle_trackers do |t|
      t.references :patient, null: false, foreign_key: true
      t.date :cycle_start_date, null: false
      t.date :cycle_end_date
      t.date :predicted_next_cycle
      t.integer :cycle_length
      t.integer :period_length
      t.string :flow_intensity
      t.json :symptoms # Array of symptom objects
      t.json :mood_data # Mood tracking data
      t.text :notes
      t.boolean :is_irregular, default: false
      t.decimal :basal_body_temperature, precision: 4, scale: 2
      t.string :cervical_mucus_type
      t.boolean :ovulation_detected, default: false
      t.date :ovulation_date
      t.json :fertility_signs
      t.boolean :contraception_used, default: false
      t.string :contraception_type

      t.timestamps
    end

    add_index :cycle_trackers, :cycle_start_date
    add_index :cycle_trackers, :predicted_next_cycle
    add_index :cycle_trackers, :ovulation_date
    add_index :cycle_trackers, [:patient_id, :cycle_start_date]
  end
end
