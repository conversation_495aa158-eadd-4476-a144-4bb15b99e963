class CreateCourseModules < ActiveRecord::Migration[8.0]
  def change
    create_table :course_modules do |t|
      t.references :course, null: false, foreign_key: true
      t.string :title, null: false
      t.text :description
      t.integer :sort_order, null: false
      t.integer :duration_minutes, default: 0
      t.boolean :is_published, default: false
      t.json :learning_objectives
      t.text :content_summary

      t.timestamps
    end

    add_index :course_modules, [:course_id, :sort_order], unique: true
    add_index :course_modules, :is_published
  end
end
