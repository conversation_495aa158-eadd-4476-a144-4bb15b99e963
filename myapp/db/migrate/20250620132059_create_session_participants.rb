class CreateSessionParticipants < ActiveRecord::Migration[8.0]
  def change
    create_table :session_participants do |t|
      t.references :group_session, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.integer :status, default: 0, null: false
      t.datetime :registered_at, null: false
      t.datetime :joined_at
      t.datetime :left_at
      t.integer :total_duration_seconds, default: 0
      t.boolean :attended, default: false
      t.decimal :attendance_percentage, precision: 5, scale: 2, default: 0.0
      t.text :feedback
      t.decimal :rating, precision: 3, scale: 2
      t.datetime :feedback_submitted_at

      t.timestamps
    end

    add_index :session_participants, [:group_session_id, :user_id], unique: true
    add_index :session_participants, :status
    add_index :session_participants, :registered_at
    add_index :session_participants, :attended
  end
end
