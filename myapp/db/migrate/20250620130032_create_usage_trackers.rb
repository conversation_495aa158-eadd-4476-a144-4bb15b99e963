class CreateUsageTrackers < ActiveRecord::Migration[8.0]
  def change
    create_table :usage_trackers do |t|
      t.references :user, null: false, foreign_key: true
      t.references :membership_feature, null: false, foreign_key: true
      t.date :period_start, null: false
      t.date :period_end, null: false
      t.integer :usage_count, default: 0
      t.boolean :limit_exceeded, default: false

      t.timestamps
    end

    add_index :usage_trackers, [:user_id, :membership_feature_id, :period_start],
              unique: true, name: 'index_usage_trackers_unique'
    add_index :usage_trackers, :period_start
    add_index :usage_trackers, :limit_exceeded
  end
end
