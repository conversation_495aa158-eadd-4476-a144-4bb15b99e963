class CreatePatients < ActiveRecord::Migration[8.0]
  def change
    create_table :patients do |t|
      t.references :user, null: false, foreign_key: true
      t.string :medical_record_number, null: false
      t.string :emergency_contact_name, null: false
      t.string :emergency_contact_phone, null: false
      t.string :blood_type
      t.integer :pregnancy_count, default: 0
      t.integer :birth_count, default: 0
      t.integer :smoking_status, default: 0
      t.integer :alcohol_consumption, default: 0

      t.timestamps
    end

    add_index :patients, :medical_record_number, unique: true
    add_index :patients, :blood_type
  end
end
