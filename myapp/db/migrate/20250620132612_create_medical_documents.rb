class CreateMedicalDocuments < ActiveRecord::Migration[8.0]
  def change
    create_table :medical_documents do |t|
      t.references :patient, null: false, foreign_key: true
      t.references :issued_by, null: true, foreign_key: { to_table: :users }
      t.references :consultation, null: true, foreign_key: true
      t.string :document_type, null: false # lab_result, prescription, report, image, etc.
      t.string :title, null: false
      t.text :description
      t.string :file_url, null: false
      t.string :file_name
      t.string :file_type # pdf, jpg, png, etc.
      t.bigint :file_size_bytes
      t.string :file_hash # For integrity verification
      t.datetime :issued_at, null: false
      t.datetime :expires_at
      t.integer :status, default: 0, null: false # active, archived, deleted
      t.boolean :is_sensitive, default: false
      t.json :access_permissions # Who can access this document
      t.json :metadata # Document-specific metadata
      t.string :external_id # ID from external system
      t.string :institution_name
      t.datetime :last_accessed_at
      t.integer :access_count, default: 0

      t.timestamps
    end

    add_index :medical_documents, :document_type
    add_index :medical_documents, :issued_at
    add_index :medical_documents, :expires_at
    add_index :medical_documents, :status
    add_index :medical_documents, :is_sensitive
    add_index :medical_documents, [:patient_id, :document_type]
    add_index :medical_documents, :file_hash, unique: true
  end
end
