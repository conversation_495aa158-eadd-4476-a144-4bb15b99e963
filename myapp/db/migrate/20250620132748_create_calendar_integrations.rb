class CreateCalendarIntegrations < ActiveRecord::Migration[8.0]
  def change
    create_table :calendar_integrations do |t|
      t.references :user, null: false, foreign_key: true
      t.string :provider, null: false # google, outlook, apple, etc.
      t.string :provider_user_id
      t.string :access_token, null: false
      t.string :refresh_token
      t.string :calendar_id
      t.string :calendar_name
      t.datetime :token_expires_at
      t.boolean :active, default: true
      t.boolean :sync_appointments, default: true
      t.boolean :sync_reminders, default: true
      t.datetime :last_sync_at
      t.json :sync_settings
      t.json :provider_metadata
      t.text :sync_errors

      t.timestamps
    end

    add_index :calendar_integrations, :provider
    add_index :calendar_integrations, :active
    add_index :calendar_integrations, :last_sync_at
    add_index :calendar_integrations, [:user_id, :provider], unique: true
  end
end
