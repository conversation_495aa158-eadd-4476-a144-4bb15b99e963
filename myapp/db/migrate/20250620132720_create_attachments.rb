class CreateAttachments < ActiveRecord::Migration[8.0]
  def change
    create_table :attachments do |t|
      t.references :attachable, polymorphic: true, null: false
      t.references :user, null: false, foreign_key: true
      t.string :file_name, null: false
      t.string :original_file_name, null: false
      t.string :file_url, null: false
      t.string :file_type, null: false
      t.bigint :file_size_bytes, null: false
      t.string :file_hash # For integrity verification
      t.string :mime_type
      t.text :description
      t.boolean :is_public, default: false
      t.boolean :is_processed, default: false
      t.json :processing_metadata
      t.string :thumbnail_url
      t.datetime :uploaded_at, null: false
      t.datetime :last_accessed_at
      t.integer :download_count, default: 0
      t.datetime :expires_at

      t.timestamps
    end

    add_index :attachments, [:attachable_type, :attachable_id]
    add_index :attachments, :file_type
    add_index :attachments, :uploaded_at
    add_index :attachments, :is_public
    add_index :attachments, :file_hash, unique: true
  end
end
