class CreateUserMemberships < ActiveRecord::Migration[8.0]
  def change
    create_table :user_memberships do |t|
      t.references :user, null: false, foreign_key: true
      t.references :membership_tier, null: false, foreign_key: true
      t.integer :status, null: false, default: 0
      t.datetime :starts_at, null: false
      t.datetime :expires_at, null: false
      t.boolean :auto_renewal, default: false

      t.timestamps
    end

    add_index :user_memberships, :status
    add_index :user_memberships, :starts_at
    add_index :user_memberships, :expires_at
    add_index :user_memberships, [:user_id, :status]
  end
end
