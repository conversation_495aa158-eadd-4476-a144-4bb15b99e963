class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      t.string :email, null: false
      t.string :password_digest, null: false
      t.string :first_name, null: false
      t.string :last_name, null: false
      t.string :phone_number, null: false
      t.date :birth_date, null: false
      t.datetime :verified_at
      t.string :locale, default: 'tr'
      t.string :timezone, default: 'Europe/Istanbul'

      t.timestamps
    end

    add_index :users, :email, unique: true
    add_index :users, :phone_number
    add_index :users, :verified_at
  end
end
