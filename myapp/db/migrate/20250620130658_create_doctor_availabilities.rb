class CreateDoctorAvailabilities < ActiveRecord::Migration[8.0]
  def change
    create_table :doctor_availabilities do |t|
      t.references :doctor, null: false, foreign_key: true
      t.integer :day_of_week, null: false
      t.string :start_time, null: false
      t.string :end_time, null: false
      t.integer :appointment_duration, null: false, default: 30
      t.integer :buffer_time, default: 5
      t.integer :max_appointments, null: false, default: 16
      t.boolean :is_available, default: true

      t.timestamps
    end

    add_index :doctor_availabilities, [:doctor_id, :day_of_week], unique: true
    add_index :doctor_availabilities, :day_of_week
    add_index :doctor_availabilities, :is_available
  end
end
