class CreateOrderItems < ActiveRecord::Migration[8.0]
  def change
    create_table :order_items do |t|
      t.references :order, null: false, foreign_key: true
      t.references :orderable, polymorphic: true, null: false
      t.integer :quantity, default: 1, null: false
      t.decimal :unit_price, precision: 10, scale: 2, null: false
      t.decimal :total_price, precision: 10, scale: 2, null: false
      t.string :item_name, null: false
      t.text :item_description
      t.json :item_metadata # Additional item-specific data
      t.decimal :discount_amount, precision: 10, scale: 2, default: 0.0
      t.string :discount_type # percentage, fixed
      t.boolean :is_refundable, default: true
      t.datetime :delivered_at
      t.integer :status, default: 0 # pending, delivered, cancelled

      t.timestamps
    end

    add_index :order_items, [:orderable_type, :orderable_id]
    add_index :order_items, :status
  end
end
