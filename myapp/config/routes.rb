Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # API versioning namespace
  namespace :api do
    namespace :v1 do
      # Authentication routes
      post 'auth/login', to: 'authentication#login'
      post 'auth/logout', to: 'authentication#logout'
      post 'auth/refresh', to: 'authentication#refresh'
      get 'auth/me', to: 'authentication#me'

      # User registration and management
      post 'users/register', to: 'users#register'
      patch 'users/verify', to: 'users#verify'
      post 'users/resend_verification', to: 'users#resend_verification'
      post 'users/forgot_password', to: 'users#forgot_password'
      patch 'users/reset_password', to: 'users#reset_password'

      # User profile management
      resources :users, only: [:show, :update, :destroy] do
        member do
          patch :change_password
          get :profile
          patch :update_profile
        end

        # User-specific nested resources
        resources :notifications, only: [:index, :show, :update, :destroy] do
          member do
            patch :mark_as_read
          end
          collection do
            patch :mark_all_as_read
            get :unread_count
          end
        end

        resources :payment_methods, except: [:show] do
          member do
            patch :set_default
          end
        end

        resources :calendar_integrations, except: [:show] do
          member do
            post :sync
            patch :toggle_active
          end
        end

        resources :usage_trackers, only: [:index, :show]
        resources :user_memberships, only: [:index, :show, :create, :update] do
          member do
            patch :cancel
            patch :renew
          end
        end
      end

      # Admin routes
      namespace :admin do
        resources :users, only: [:index, :show, :update, :destroy] do
          member do
            patch :verify
            patch :suspend
            patch :activate
          end
        end

        resources :doctors, only: [:index, :show, :update] do
          member do
            patch :verify
            patch :reject
            patch :suspend
          end

          resources :doctor_verifications, only: [:index, :show, :update]
        end

        resources :patients, only: [:index, :show]
        resources :consultations, only: [:index, :show]
        resources :appointments, only: [:index, :show, :update]
        resources :orders, only: [:index, :show, :update]
        resources :payments, only: [:index, :show]

        # System management
        resources :membership_tiers do
          resources :tier_features, except: [:show]
        end

        resources :membership_features
        resources :courses, only: [:index, :show, :update] do
          member do
            patch :approve
            patch :reject
          end
        end

        # Analytics and reports
        get 'analytics/dashboard', to: 'analytics#dashboard'
        get 'analytics/users', to: 'analytics#users'
        get 'analytics/consultations', to: 'analytics#consultations'
        get 'analytics/revenue', to: 'analytics#revenue'
      end

      # Doctor-specific routes
      resources :doctors, only: [:show, :update] do
        member do
          get :profile
          patch :update_profile
          get :availability
          patch :update_availability
          get :statistics
        end

        resources :doctor_availabilities, except: [:show]
        resources :medical_licenses
        resources :doctor_verifications, only: [:index, :show, :create]

        # Doctor's appointments and consultations
        resources :appointments, only: [:index, :show, :update] do
          member do
            patch :accept
            patch :reject
            patch :start
            patch :complete
            patch :cancel
          end
        end

        resources :consultations, only: [:index, :show, :create, :update] do
          member do
            patch :start
            patch :complete
          end

          resources :prescriptions, except: [:destroy]
        end

        # Doctor's courses and group sessions
        resources :courses do
          resources :course_modules do
            resources :course_videos
          end

          member do
            patch :publish
            patch :unpublish
            get :enrollments
            get :analytics
          end
        end

        resources :group_sessions do
          member do
            patch :start
            patch :complete
            patch :cancel
            get :participants
          end

          resources :session_participants, only: [:index, :show, :update, :destroy]
        end
      end

      # Patient-specific routes
      resources :patients, only: [:show, :update] do
        member do
          get :profile
          patch :update_profile
          get :health_summary
        end

        # Health tracking
        resources :health_profiles, only: [:show, :create, :update]
        resources :cycle_trackers do
          member do
            get :predictions
            get :fertility_window
          end
        end

        resources :pregnancy_trackers do
          member do
            get :weekly_info
            get :milestones
          end
        end

        resources :health_metrics do
          collection do
            get :trends
            get :summary
          end
        end

        resources :ai_health_insights, only: [:index, :show, :update] do
          member do
            patch :acknowledge
          end
        end

        resources :risk_assessments, only: [:index, :show]

        # Medical records and documents
        resources :medical_documents do
          member do
            get :download
            patch :share
          end
        end

        # Appointments and consultations
        resources :appointments do
          member do
            patch :cancel
            patch :reschedule
          end
        end

        resources :consultations, only: [:index, :show] do
          resources :prescriptions, only: [:index, :show]
        end

        # Course enrollment and progress
        resources :course_enrollments, only: [:index, :show, :create, :destroy] do
          member do
            get :progress
            patch :complete
          end
        end

        resources :video_progresses, only: [:show, :update] do
          member do
            patch :mark_watched
          end
        end

        # Group sessions
        resources :session_participants, only: [:index, :show, :create, :destroy] do
          member do
            patch :join
            patch :leave
            post :feedback
          end
        end
      end

      # Public course catalog
      resources :courses, only: [:index, :show] do
        member do
          get :preview
        end

        collection do
          get :featured
          get :categories
          get :search
        end

        resources :course_modules, only: [:index, :show] do
          resources :course_videos, only: [:index, :show]
        end
      end

      # Group sessions (public listing)
      resources :group_sessions, only: [:index, :show] do
        collection do
          get :upcoming
          get :categories
        end
      end

      # Appointment booking
      resources :appointments, only: [:create, :show, :update] do
        collection do
          get :available_slots
        end
      end

      # Orders and payments
      resources :orders do
        member do
          patch :confirm
          patch :cancel
          get :receipt
        end

        resources :order_items, only: [:index, :show]
        resources :payments, only: [:index, :show, :create]
      end

      # Membership tiers (public)
      resources :membership_tiers, only: [:index, :show] do
        resources :tier_features, only: [:index]
      end

      # Search and filtering
      get 'search/doctors', to: 'search#doctors'
      get 'search/courses', to: 'search#courses'
      get 'search/group_sessions', to: 'search#group_sessions'

      # File uploads and attachments
      resources :attachments, only: [:show, :create, :destroy] do
        member do
          get :download
        end
      end

      # Utility routes
      get 'specializations', to: 'utilities#specializations'
      get 'countries', to: 'utilities#countries'
      get 'timezones', to: 'utilities#timezones'
      get 'currencies', to: 'utilities#currencies'
    end
  end

  # Defines the root path route ("/")
  # root "posts#index"
end
