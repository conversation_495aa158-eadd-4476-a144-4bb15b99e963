# Cucumber BDD Testing Setup

This project now includes Cucumber for Behavior-Driven Development (BDD) testing of the API endpoints.

## What's Included

### 1. Core Appointment Booking Scenario
The main scenario tests the complete user journey:
- Patient searches for doctors by specialization
- Views doctor profiles and availability
- Books an appointment
- Receives confirmation
- Views appointment details

### 2. Additional Test Scenarios
- Doctor search with filters (rating, fees, specialization)
- Authentication error handling
- Appointment viewing and management

## Running the Tests

### Run all Cucumber scenarios:
```bash
bundle exec cucumber
```

### Run specific scenarios:
```bash
# Run only API scenarios
bundle exec cucumber --tags @api

# Run specific feature
bundle exec cucumber features/appointment_booking.feature

# Run with specific format
bundle exec cucumber --format pretty
```

### Run in different environments:
```bash
# Test environment (default)
RAILS_ENV=test bundle exec cucumber

# With database cleanup
bundle exec cucumber --tags @api
```

## Test Structure

### Features (`features/`)
- `appointment_booking.feature` - Main user journey scenarios

### Step Definitions (`features/step_definitions/`)
- `appointment_booking_steps.rb` - Step implementations for appointment booking

### Support Files (`features/support/`)
- `env.rb` - Cucumber environment setup
- `api_helpers.rb` - API testing helper methods
- `factory_bot.rb` - FactoryBot configuration

### Test Data (`test/factories/`)
- `users.rb` - User factory with traits
- `doctors.rb` - Doctor factory with specializations
- `patients.rb` - Patient factory with health data
- `appointments.rb` - Appointment factory with statuses
- `doctor_availabilities.rb` - Doctor availability schedules

## Key Features of the Test Suite

1. **API-First Testing**: Tests actual HTTP endpoints
2. **JWT Authentication**: Proper token-based auth testing
3. **Comprehensive Data**: Realistic test data with FactoryBot
4. **Error Scenarios**: Tests both success and failure cases
5. **Database Cleanup**: Automatic cleanup between scenarios

## Example Scenario

```gherkin
Scenario: Patient successfully books an appointment with a doctor
  Given I am logged in as a patient
  When I search for doctors by specialization "Gynecology"
  Then I should see a list of available doctors
  And each doctor should have consultation fees displayed
  
  When I select a doctor from the search results
  Then I should see the doctor's detailed profile
  And I should see their available time slots
  
  When I select an available time slot for tomorrow
  And I provide appointment details:
    | complaint_summary | I have been experiencing irregular periods |
    | appointment_type  | consultation                               |
  And I confirm the appointment booking
  Then the appointment should be created successfully
  And I should receive a booking confirmation
```

## Next Steps

To extend the test suite:

1. Add more scenarios for different user types (doctors, admins)
2. Test consultation and prescription workflows
3. Add payment and billing scenarios
4. Test notification systems
5. Add performance and load testing scenarios

## Troubleshooting

If tests fail:
1. Ensure database is properly set up: `rails db:test:prepare`
2. Check that all required gems are installed: `bundle install`
3. Verify factory data is valid
4. Check API endpoint implementations match the expected routes
