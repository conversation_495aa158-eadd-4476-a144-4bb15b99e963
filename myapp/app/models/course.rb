class Course < ApplicationRecord
  belongs_to :instructor, class_name: 'Doctor'
  has_many :course_modules, dependent: :destroy
  has_many :course_videos, through: :course_modules
  has_many :course_enrollments, dependent: :destroy
  has_many :patients, through: :course_enrollments
  has_many :order_items, as: :orderable, dependent: :destroy

  # Enums
  enum :status, {
    draft: 0,
    published: 1,
    archived: 2,
    suspended: 3
  }

  enum :difficulty_level, {
    beginner: 0,
    intermediate: 1,
    advanced: 2,
    expert: 3
  }

  # Validations
  validates :title, presence: true, length: { minimum: 5, maximum: 200 }
  validates :description, presence: true, length: { minimum: 20 }
  validates :slug, presence: true, uniqueness: true
  validates :category, presence: true
  validates :difficulty_level, presence: true
  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :currency, inclusion: { in: %w[TRY USD EUR] }

  # Callbacks
  before_validation :generate_slug, on: :create
  after_update :update_published_at, if: :saved_change_to_status?

  # Scopes
  scope :published, -> { where(status: :published) }
  scope :featured, -> { where(is_featured: true) }
  scope :free, -> { where(price: 0) }
  scope :paid, -> { where('price > 0') }
  scope :by_category, ->(category) { where(category: category) }
  scope :by_difficulty, ->(level) { where(difficulty_level: level) }
  scope :popular, -> { order(enrollment_count: :desc) }
  scope :highest_rated, -> { where.not(rating: nil).order(rating: :desc) }

  # JSON columns don't need serialization in Rails 8

  # Instance methods
  def is_published?
    status == 'published'
  end

  def is_free?
    price == 0
  end

  def is_paid?
    price > 0
  end

  def total_duration_minutes
    course_modules.sum(:duration_minutes)
  end

  def total_videos_count
    course_videos.count
  end

  def average_rating
    rating || 0.0
  end

  def enrollment_rate
    return 0 if enrollment_count == 0
    (course_enrollments.completed.count.to_f / enrollment_count * 100).round(1)
  end

  def completion_rate
    return 0 if enrollment_count == 0
    (course_enrollments.completed.count.to_f / enrollment_count * 100).round(1)
  end

  def can_be_enrolled_by?(patient)
    return false unless is_published?
    return false if course_enrollments.exists?(patient: patient)
    true
  end

  def enroll_patient!(patient)
    return false unless can_be_enrolled_by?(patient)

    course_enrollments.create!(
      patient: patient,
      enrolled_at: Time.current,
      status: :active
    )
  end

  def update_stats!
    self.enrollment_count = course_enrollments.count
    self.rating = course_enrollments.where.not(rating: nil).average(:rating)
    self.review_count = course_enrollments.where.not(review: nil).count
    save!
  end

  def course_outline
    course_modules.includes(:course_videos).order(:sort_order).map do |mod|
      {
        title: mod.title,
        duration_minutes: mod.duration_minutes,
        videos_count: mod.course_videos.count,
        videos: mod.course_videos.order(:sort_order).map do |video|
          {
            title: video.title,
            duration_seconds: video.duration_seconds,
            is_preview: video.is_preview?
          }
        end
      }
    end
  end

  def next_module_for(patient)
    enrollment = course_enrollments.find_by(patient: patient)
    return nil unless enrollment

    # Find the first module that hasn't been completed
    course_modules.order(:sort_order).find do |mod|
      !mod.completed_by?(patient)
    end
  end

  private

  def generate_slug
    return if slug.present?
    base_slug = title.parameterize
    counter = 1
    self.slug = base_slug

    while Course.exists?(slug: slug)
      self.slug = "#{base_slug}-#{counter}"
      counter += 1
    end
  end

  def update_published_at
    if status == 'published' && published_at.nil?
      update_column(:published_at, Time.current)
    elsif status != 'published'
      update_column(:published_at, nil)
    end
  end
end
