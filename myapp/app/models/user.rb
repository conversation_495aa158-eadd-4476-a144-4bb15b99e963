class User < ApplicationRecord
  has_secure_password

  # Associations
  has_one :admin, dependent: :destroy
  has_one :doctor, dependent: :destroy
  has_one :patient, dependent: :destroy
  has_many :user_memberships, dependent: :destroy
  has_many :membership_tiers, through: :user_memberships
  has_many :usage_trackers, dependent: :destroy
  has_many :payment_methods, dependent: :destroy
  has_many :notifications, dependent: :destroy

  # Validations
  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :first_name, presence: true, length: { minimum: 2, maximum: 50 }
  validates :last_name, presence: true, length: { minimum: 2, maximum: 50 }
  validates :phone_number, presence: true, format: { with: /\A\+?[1-9]\d{1,14}\z/ }
  validates :birth_date, presence: true
  validates :locale, inclusion: { in: %w[tr en] }
  validates :timezone, presence: true

  # Callbacks
  before_save :normalize_email
  before_validation :set_defaults

  # Scopes
  scope :verified, -> { where.not(verified_at: nil) }
  scope :unverified, -> { where(verified_at: nil) }

  # Instance methods
  def full_name
    "#{first_name} #{last_name}"
  end

  def verified?
    verified_at.present?
  end

  def user_type
    return 'admin' if admin.present?
    return 'doctor' if doctor.present?
    return 'patient' if patient.present?
    'user'
  end

  def current_membership
    user_memberships.active.current.first
  end

  private

  def normalize_email
    self.email = email.downcase.strip if email.present?
  end

  def set_defaults
    self.locale ||= 'tr'
    self.timezone ||= 'Europe/Istanbul'
  end
end
