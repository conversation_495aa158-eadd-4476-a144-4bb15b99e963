class SessionParticipant < ApplicationRecord
  belongs_to :group_session
  belongs_to :user

  # Enums
  enum :status, {
    registered: 0,
    confirmed: 1,
    attended: 2,
    no_show: 3,
    cancelled: 4
  }

  # Validations
  validates :registered_at, presence: true
  validates :attendance_percentage, numericality: { in: 0.0..100.0 }, allow_nil: true
  validates :rating, numericality: { in: 1.0..5.0 }, allow_nil: true

  # Callbacks
  after_create :increment_session_participants
  after_destroy :decrement_session_participants
  after_update :update_session_attendance, if: :saved_change_to_attended?

  # Scopes
  scope :attended, -> { where(attended: true) }
  scope :no_show, -> { where(attended: false) }
  scope :with_feedback, -> { where.not(feedback: nil) }

  # Instance methods
  def mark_as_attended!(duration_seconds = nil)
    update!(
      attended: true,
      joined_at: Time.current,
      total_duration_seconds: duration_seconds || 0
    )
  end

  def mark_as_left!(left_time = Time.current)
    return unless joined_at

    duration = (left_time - joined_at).to_i
    total_session_duration = group_session.duration_minutes * 60
    attendance_pct = (duration.to_f / total_session_duration * 100).round(2)

    update!(
      left_at: left_time,
      total_duration_seconds: duration,
      attendance_percentage: attendance_pct
    )
  end

  def submit_feedback!(rating, feedback_text = nil)
    update!(
      rating: rating,
      feedback: feedback_text,
      feedback_submitted_at: Time.current
    )
  end

  def has_feedback?
    feedback.present?
  end

  def session_duration_formatted
    return '0m' if total_duration_seconds == 0

    hours = total_duration_seconds / 3600
    minutes = (total_duration_seconds % 3600) / 60

    if hours > 0
      "#{hours}h #{minutes}m"
    else
      "#{minutes}m"
    end
  end

  def attendance_status
    return 'registered' unless group_session.completed?
    attended? ? 'attended' : 'no_show'
  end

  def can_join_session?
    group_session.live? && !attended?
  end

  def can_leave_feedback?
    attended? && group_session.completed?
  end

  private

  def increment_session_participants
    group_session.increment!(:current_participants)
  end

  def decrement_session_participants
    group_session.decrement!(:current_participants)
  end

  def update_session_attendance
    # Update group session attendance statistics if needed
  end
end
