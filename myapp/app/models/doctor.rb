class Doctor < ApplicationRecord
  belongs_to :user
  has_many :doctor_availabilities, dependent: :destroy
  has_many :medical_licenses, dependent: :destroy
  has_one :doctor_verification, dependent: :destroy
  has_many :appointments, dependent: :destroy
  has_many :consultations, dependent: :destroy
  has_many :prescriptions, dependent: :destroy
  has_many :courses, foreign_key: 'instructor_id', dependent: :nullify
  has_many :group_sessions, foreign_key: 'host_id', dependent: :nullify

  # Enums
  enum :verification_status, {
    pending: 0,
    under_review: 1,
    verified: 2,
    rejected: 3
  }

  # Validations
  validates :license_number, presence: true, uniqueness: true
  validates :specialization, presence: true
  validates :graduated_university, presence: true
  validates :graduation_year, presence: true,
            numericality: { greater_than: 1950, less_than_or_equal_to: Date.current.year }
  validates :years_experience, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :consultation_fee_day, presence: true, numericality: { greater_than: 0 }
  validates :consultation_fee_night, presence: true, numericality: { greater_than: 0 }
  validates :consultation_fee_emergency, presence: true, numericality: { greater_than: 0 }
  validates :rating, numericality: { in: 0.0..5.0 }, allow_nil: true
  validates :total_consultations, numericality: { greater_than_or_equal_to: 0 }
  validates :user_id, uniqueness: true

  # JSON columns don't need serialization in Rails 8

  # Scopes
  scope :verified, -> { where(verification_status: :verified) }
  scope :available, -> { verified.joins(:doctor_availabilities).where(doctor_availabilities: { is_available: true }) }
  scope :by_specialization, ->(spec) { where(specialization: spec) }

  # Instance methods
  def verified?
    verification_status == 'verified'
  end

  def available_today?
    doctor_availabilities.where(day_of_week: Date.current.wday, is_available: true).exists?
  end

  def consultation_fee_for_time(time_type = :day)
    case time_type.to_sym
    when :day then consultation_fee_day
    when :night then consultation_fee_night
    when :emergency then consultation_fee_emergency
    else consultation_fee_day
    end
  end

  def average_rating
    rating || 0.0
  end
end
