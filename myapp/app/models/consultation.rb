class Consultation < ApplicationRecord
  belongs_to :appointment, optional: true
  belongs_to :patient
  belongs_to :doctor
  has_many :prescriptions, dependent: :destroy
  has_many :medical_documents, dependent: :destroy

  # Enums
  enum :status, {
    scheduled: 0,
    in_progress: 1,
    completed: 2,
    cancelled: 3
  }

  enum :consultation_type, {
    initial: 0,
    follow_up: 1,
    emergency: 2,
    second_opinion: 3,
    routine_checkup: 4
  }

  enum :consultation_method, {
    video: 0,
    phone: 1,
    in_person: 2,
    chat: 3
  }

  # Validations
  validates :consultation_type, presence: true
  validates :chief_complaint, presence: true
  validates :consultation_method, presence: true
  validate :started_at_before_ended_at, if: :ended_at?

  # Callbacks
  before_save :calculate_duration, if: :started_at_changed? || :ended_at_changed?
  after_update :update_appointment_status, if: :status_changed?

  # Scopes
  scope :completed, -> { where(status: :completed) }
  scope :today, -> { where(started_at: Date.current.beginning_of_day..Date.current.end_of_day) }
  scope :this_week, -> { where(started_at: Date.current.beginning_of_week..Date.current.end_of_week) }
  scope :by_method, ->(method) { where(consultation_method: method) }

  # JSON columns don't need serialization in Rails 8

  # Instance methods
  def duration_in_minutes
    return 0 unless started_at && ended_at
    ((ended_at - started_at) / 60).round
  end

  def is_completed?
    status == 'completed'
  end

  def is_in_progress?
    status == 'in_progress'
  end

  def can_be_started?
    scheduled? && (appointment.nil? || appointment.scheduled_at <= Time.current)
  end

  def can_be_completed?
    in_progress?
  end

  def has_prescription?
    prescription_issued?
  end

  def start_consultation!
    update!(
      status: :in_progress,
      started_at: Time.current
    )
  end

  def complete_consultation!
    update!(
      status: :completed,
      ended_at: Time.current
    )
  end

  def total_fee
    consultation_fee || (appointment&.consultation_fee || 0)
  end

  def consultation_summary
    {
      chief_complaint: chief_complaint,
      assessment: assessment,
      plan: plan,
      prescriptions_count: prescriptions.count,
      duration_minutes: duration_minutes,
      follow_up_required: next_follow_up_date.present?
    }
  end

  private

  def calculate_duration
    return unless started_at && ended_at
    self.duration_minutes = duration_in_minutes
  end

  def started_at_before_ended_at
    return unless started_at && ended_at
    errors.add(:ended_at, 'must be after start time') if ended_at <= started_at
  end

  def update_appointment_status
    return unless appointment

    case status
    when 'in_progress'
      appointment.update(status: :in_progress, started_at: started_at)
    when 'completed'
      appointment.update(status: :completed, ended_at: ended_at)
    when 'cancelled'
      appointment.update(status: :cancelled, cancelled_at: Time.current)
    end
  end
end
