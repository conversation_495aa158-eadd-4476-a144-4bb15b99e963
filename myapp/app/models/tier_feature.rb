class TierFeature < ApplicationRecord
  belongs_to :membership_tier
  belongs_to :membership_feature

  # Validations
  validates :membership_tier_id, uniqueness: { scope: :membership_feature_id }
  validates :limit_value, numericality: { greater_than: 0 }, allow_nil: true
  validates :is_unlimited, inclusion: { in: [true, false] }
  validates :is_included, inclusion: { in: [true, false] }

  # Validation logic
  validate :limit_value_consistency
  validate :feature_type_consistency

  # Scopes
  scope :included, -> { where(is_included: true) }
  scope :unlimited, -> { where(is_unlimited: true) }
  scope :limited, -> { where(is_unlimited: false, is_included: true) }

  # Instance methods
  def unlimited?
    is_unlimited
  end

  def included?
    is_included
  end

  def has_limit?
    !is_unlimited && limit_value.present?
  end

  def effective_limit
    return Float::INFINITY if is_unlimited
    return 0 unless is_included
    limit_value || 0
  end

  private

  def limit_value_consistency
    if is_unlimited && limit_value.present?
      errors.add(:limit_value, "should be nil when feature is unlimited")
    elsif !is_unlimited && is_included && membership_feature.countable? && limit_value.blank?
      errors.add(:limit_value, "is required for countable features that are not unlimited")
    end
  end

  def feature_type_consistency
    return unless membership_feature

    case membership_feature.feature_type
    when 'boolean'
      if limit_value.present?
        errors.add(:limit_value, "should be nil for boolean features")
      end
    when 'countable', 'time_based'
      if is_included && !is_unlimited && limit_value.blank?
        errors.add(:limit_value, "is required for #{membership_feature.feature_type} features")
      end
    end
  end
end
