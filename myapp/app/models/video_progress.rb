class VideoProgress < ApplicationRecord
  belongs_to :course_video
  belongs_to :patient

  # Validations
  validates :watched_seconds, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :progress_percentage, presence: true, numericality: { in: 0.0..100.0 }

  # Callbacks
  before_save :update_completion_status

  # Scopes
  scope :completed, -> { where(completed: true) }
  scope :in_progress, -> { where(completed: false).where('progress_percentage > 0') }
  scope :recent, -> { where('last_watched_at > ?', 7.days.ago) }

  # JSON columns don't need serialization in Rails 8

  # Instance methods
  def completion_percentage
    progress_percentage
  end

  def is_completed?
    completed?
  end

  def duration_watched
    watched_seconds
  end

  def duration_watched_formatted
    return '0s' if watched_seconds == 0

    hours = watched_seconds / 3600
    minutes = (watched_seconds % 3600) / 60
    seconds = watched_seconds % 60

    if hours > 0
      format('%02d:%02d:%02d', hours, minutes, seconds)
    else
      format('%02d:%02d', minutes, seconds)
    end
  end

  def remaining_seconds
    return 0 if completed?
    [course_video.duration_seconds - watched_seconds, 0].max
  end

  def watch_time_today
    return 0 unless watch_sessions.is_a?(Array)
    
    today_sessions = watch_sessions.select do |session|
      session['date'] == Date.current.to_s
    end
    
    today_sessions.sum { |session| session['duration'] || 0 }
  end

  def add_watch_session!(duration_seconds)
    self.watch_sessions ||= []
    
    today = Date.current.to_s
    existing_session = watch_sessions.find { |s| s['date'] == today }
    
    if existing_session
      existing_session['duration'] = (existing_session['duration'] || 0) + duration_seconds
      existing_session['last_watched_at'] = Time.current.to_s
    else
      watch_sessions << {
        'date' => today,
        'duration' => duration_seconds,
        'first_watched_at' => Time.current.to_s,
        'last_watched_at' => Time.current.to_s
      }
    end
    
    save!
  end

  private

  def update_completion_status
    # Mark as completed if watched 90% or more
    self.completed = progress_percentage >= 90.0
    
    if completed? && completed_at.nil?
      self.completed_at = Time.current
    elsif !completed?
      self.completed_at = nil
    end
  end
end
