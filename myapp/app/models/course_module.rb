class CourseModule < ApplicationRecord
  belongs_to :course
  has_many :course_videos, dependent: :destroy

  # Validations
  validates :title, presence: true, length: { minimum: 3, maximum: 200 }
  validates :sort_order, presence: true, uniqueness: { scope: :course_id }

  # Callbacks
  after_save :update_course_duration
  after_destroy :update_course_duration

  # Scopes
  scope :published, -> { where(is_published: true) }
  scope :ordered, -> { order(:sort_order) }

  # JSON columns don't need serialization in Rails 8

  # Instance methods
  def total_duration_seconds
    course_videos.sum(:duration_seconds)
  end

  def total_duration_minutes
    (total_duration_seconds / 60.0).ceil
  end

  def videos_count
    course_videos.count
  end

  def published_videos_count
    course_videos.published.count
  end

  def completed_by?(patient)
    return false unless patient

    # Module is completed if all videos are completed
    total_videos = course_videos.published.count
    return false if total_videos == 0

    completed_videos = course_videos.published.joins(:video_progresses)
                                   .where(video_progresses: { patient: patient, completed: true })
                                   .count

    completed_videos == total_videos
  end

  def progress_for(patient)
    return 0.0 unless patient

    total_videos = course_videos.published.count
    return 100.0 if total_videos == 0

    completed_videos = course_videos.published.joins(:video_progresses)
                                   .where(video_progresses: { patient: patient, completed: true })
                                   .count

    (completed_videos.to_f / total_videos * 100).round(1)
  end

  def next_video_for(patient)
    return course_videos.published.ordered.first unless patient

    # Find the first video that hasn't been completed
    course_videos.published.ordered.find do |video|
      progress = video.video_progresses.find_by(patient: patient)
      progress.nil? || !progress.completed?
    end
  end

  private

  def update_course_duration
    course.update_column(:duration_minutes, course.course_modules.sum(:duration_minutes))
  end
end
