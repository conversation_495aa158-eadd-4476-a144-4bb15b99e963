class HealthProfile < ApplicationRecord
  belongs_to :patient

  # Validations
  validates :height, numericality: { greater_than: 0, less_than: 300 }, allow_nil: true
  validates :weight, numericality: { greater_than: 0, less_than: 1000 }, allow_nil: true
  validates :patient_id, uniqueness: true

  # JSON columns don't need serialization in Rails 8

  # Callbacks
  before_save :calculate_bmi

  # Instance methods
  def bmi
    return nil unless height && weight
    (weight / (height / 100.0) ** 2).round(1)
  end

  def bmi_category
    bmi_value = bmi
    return nil unless bmi_value

    case bmi_value
    when 0..18.4 then 'Underweight'
    when 18.5..24.9 then 'Normal weight'
    when 25.0..29.9 then 'Overweight'
    when 30.0..34.9 then 'Obesity Class I'
    when 35.0..39.9 then 'Obesity Class II'
    else 'Obesity Class III'
    end
  end

  def has_allergies?
    allergies.present? && allergies.any?
  end

  def has_chronic_conditions?
    chronic_conditions.present? && chronic_conditions.any?
  end

  def current_medications?
    medications.present? && medications.any?
  end

  def has_family_history?
    family_history.present? && family_history.any?
  end

  def health_summary
    summary = []
    summary << "BMI: #{bmi} (#{bmi_category})" if bmi
    summary << "Allergies: #{allergies.join(', ')}" if has_allergies?
    summary << "Chronic conditions: #{chronic_conditions.join(', ')}" if has_chronic_conditions?
    summary << "Current medications: #{medications.count} items" if current_medications?
    summary.join(' | ')
  end

  def risk_factors
    factors = []
    factors << 'High BMI' if bmi && bmi >= 30
    factors << 'Low BMI' if bmi && bmi < 18.5
    factors << 'Multiple chronic conditions' if chronic_conditions&.count.to_i > 2
    factors << 'Multiple medications' if medications&.count.to_i > 5
    factors
  end

  private

  def calculate_bmi
    # BMI is calculated in the bmi method, this is just a placeholder
    # for any additional calculations we might want to store
  end
end
