class Appointment < ApplicationRecord
  belongs_to :patient
  belongs_to :doctor
  has_one :consultation, dependent: :destroy

  # Enums
  enum :status, {
    scheduled: 0,
    confirmed: 1,
    in_progress: 2,
    completed: 3,
    cancelled: 4,
    no_show: 5
  }

  enum :appointment_type, {
    consultation: 0,
    follow_up: 1,
    emergency: 2,
    routine_checkup: 3,
    specialist_referral: 4
  }

  # Validations
  validates :scheduled_at, presence: true
  validates :ends_at, presence: true
  validates :duration_minutes, presence: true, numericality: { greater_than: 0 }
  validates :consultation_fee, presence: true, numericality: { greater_than: 0 }
  validates :appointment_type, presence: true
  validate :ends_at_after_scheduled_at
  validate :appointment_in_future, on: :create, unless: :skip_future_validation
  validate :doctor_availability, on: :create, unless: :skip_availability_validation

  attr_accessor :skip_future_validation, :skip_availability_validation

  # Callbacks
  before_validation :set_ends_at, if: :scheduled_at_changed?
  before_validation :set_consultation_fee, if: :new_record?

  # Scopes
  scope :upcoming, -> { where('scheduled_at > ?', Time.current) }
  scope :past, -> { where('scheduled_at < ?', Time.current) }
  scope :today, -> { where(scheduled_at: Date.current.beginning_of_day..Date.current.end_of_day) }
  scope :this_week, -> { where(scheduled_at: Date.current.beginning_of_week..Date.current.end_of_week) }
  scope :active, -> { where.not(status: [:cancelled, :no_show]) }

  # Instance methods
  def duration
    duration_minutes.minutes
  end

  def can_be_cancelled?
    scheduled? && scheduled_at > 24.hours.from_now
  end

  def can_be_rescheduled?
    scheduled? && scheduled_at > 2.hours.from_now
  end

  def is_upcoming?
    scheduled_at > Time.current
  end

  def is_today?
    scheduled_at.to_date == Date.current
  end

  def time_until_appointment
    return nil if scheduled_at <= Time.current
    ((scheduled_at - Time.current) / 1.hour).round(1)
  end

  def consultation_fee_for_time
    hour = scheduled_at.hour
    case hour
    when 22..23, 0..6
      doctor.consultation_fee_night
    when 7..21
      doctor.consultation_fee_day
    else
      doctor.consultation_fee_emergency
    end
  end

  private

  def set_ends_at
    return unless scheduled_at && duration_minutes
    self.ends_at = scheduled_at + duration_minutes.minutes
  end

  def set_consultation_fee
    return unless doctor && scheduled_at
    self.consultation_fee = consultation_fee_for_time
  end

  def ends_at_after_scheduled_at
    return unless scheduled_at && ends_at
    errors.add(:ends_at, 'must be after scheduled time') if ends_at <= scheduled_at
  end

  def appointment_in_future
    return unless scheduled_at
    errors.add(:scheduled_at, 'must be in the future') if scheduled_at <= Time.current
  end

  def doctor_availability
    return unless doctor && scheduled_at
    day_of_week = scheduled_at.wday
    availability = doctor.doctor_availabilities.find_by(day_of_week: day_of_week)
    
    unless availability&.is_available?
      errors.add(:scheduled_at, 'Doctor is not available on this day')
    end
  end
end
