class Admin < ApplicationRecord
  belongs_to :user

  # Enums
  enum :role, {
    admin: 0,
    super_admin: 1,
    moderator: 2
  }

  # Validations
  validates :role, presence: true
  validates :department, presence: true
  validates :user_id, uniqueness: true

  # JSON columns don't need serialization in Rails 8

  # Callbacks
  before_validation :set_default_permissions

  # Instance methods
  def has_permission?(permission)
    permissions&.dig(permission.to_s) == true
  end

  def full_permissions?
    super_admin?
  end

  private

  def set_default_permissions
    self.permissions ||= {
      'users' => false,
      'doctors' => false,
      'content' => false,
      'billing' => false
    }
  end
end
