class DoctorAvailability < ApplicationRecord
  belongs_to :doctor

  # Validations
  validates :day_of_week, presence: true, inclusion: { in: 0..6 }
  validates :start_time, presence: true, format: { with: /\A([01]?[0-9]|2[0-3]):[0-5][0-9]\z/ }
  validates :end_time, presence: true, format: { with: /\A([01]?[0-9]|2[0-3]):[0-5][0-9]\z/ }
  validates :appointment_duration, presence: true, numericality: { greater_than: 0, less_than_or_equal_to: 240 }
  validates :buffer_time, presence: true, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 60 }
  validates :max_appointments, presence: true, numericality: { greater_than: 0 }
  validates :is_available, inclusion: { in: [true, false] }
  validates :doctor_id, uniqueness: { scope: :day_of_week }
  validate :end_time_after_start_time

  # Scopes
  scope :available, -> { where(is_available: true) }
  scope :for_day, ->(day) { where(day_of_week: day) }
  scope :for_today, -> { where(day_of_week: Date.current.wday) }

  # Class methods
  def self.day_names
    %w[Sunday Monday Tuesday Wednesday Thursday Friday Saturday]
  end

  # Instance methods
  def day_name
    self.class.day_names[day_of_week]
  end

  def available_slots(date = Date.current)
    return [] unless is_available && date.wday == day_of_week

    slots = []
    current_time = Time.parse("#{date} #{start_time}")
    end_time_obj = Time.parse("#{date} #{end_time}")

    while current_time + appointment_duration.minutes <= end_time_obj
      slots << current_time.strftime("%H:%M")
      current_time += (appointment_duration + buffer_time).minutes
    end

    slots
  end

  def total_duration_minutes
    start_minutes = time_to_minutes(start_time)
    end_minutes = time_to_minutes(end_time)
    end_minutes - start_minutes
  end

  def theoretical_max_appointments
    (total_duration_minutes / (appointment_duration + buffer_time)).floor
  end

  private

  def end_time_after_start_time
    return unless start_time && end_time

    start_minutes = time_to_minutes(start_time)
    end_minutes = time_to_minutes(end_time)

    if end_minutes <= start_minutes
      errors.add(:end_time, "must be after start time")
    end
  end

  def time_to_minutes(time_string)
    hours, minutes = time_string.split(':').map(&:to_i)
    hours * 60 + minutes
  end
end
