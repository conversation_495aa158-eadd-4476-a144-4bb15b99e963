class GroupSession < ApplicationRecord
  belongs_to :host, class_name: 'Doctor'
  has_many :session_participants, dependent: :destroy
  has_many :users, through: :session_participants

  # Enums
  enum :status, {
    scheduled: 0,
    live: 1,
    completed: 2,
    cancelled: 3
  }

  enum :session_type, {
    webinar: 0,
    workshop: 1,
    q_and_a: 2,
    support_group: 3,
    consultation: 4
  }

  # Validations
  validates :title, presence: true, length: { minimum: 5, maximum: 200 }
  validates :description, presence: true
  validates :scheduled_at, presence: true
  validates :ends_at, presence: true
  validates :duration_minutes, presence: true, numericality: { greater_than: 0 }
  validates :max_participants, presence: true, numericality: { greater_than: 0 }
  validate :ends_at_after_scheduled_at

  # Callbacks
  before_validation :set_ends_at, if: :scheduled_at_changed?

  # Scopes
  scope :upcoming, -> { where('scheduled_at > ?', Time.current) }
  scope :past, -> { where('scheduled_at < ?', Time.current) }
  scope :today, -> { where(scheduled_at: Date.current.beginning_of_day..Date.current.end_of_day) }
  scope :available, -> { where('current_participants < max_participants') }

  # JSON columns don't need serialization in Rails 8

  # Instance methods
  def is_full?
    current_participants >= max_participants
  end

  def has_space?
    current_participants < max_participants
  end

  def available_spots
    max_participants - current_participants
  end

  def can_join?(user)
    return false if is_full?
    return false if scheduled?
    !session_participants.exists?(user: user)
  end

  def join!(user)
    return false unless can_join?(user)

    session_participants.create!(
      user: user,
      status: :registered,
      registered_at: Time.current
    )
  end

  def start_session!
    return false unless scheduled?

    update!(
      status: :live,
      started_at: Time.current
    )
  end

  def end_session!
    return false unless live?

    update!(
      status: :completed,
      ended_at: Time.current
    )
  end

  def duration_formatted
    hours = duration_minutes / 60
    minutes = duration_minutes % 60

    if hours > 0
      "#{hours}h #{minutes}m"
    else
      "#{minutes}m"
    end
  end

  def is_upcoming?
    scheduled_at > Time.current
  end

  def is_today?
    scheduled_at.to_date == Date.current
  end

  def time_until_session
    return nil if scheduled_at <= Time.current
    ((scheduled_at - Time.current) / 1.hour).round(1)
  end

  def attendance_rate
    return 0 if current_participants == 0
    attended_count = session_participants.where(attended: true).count
    (attended_count.to_f / current_participants * 100).round(1)
  end

  def session_summary
    {
      title: title,
      host: host.user.full_name,
      scheduled_at: scheduled_at,
      duration_minutes: duration_minutes,
      participants_count: current_participants,
      attendance_rate: attendance_rate,
      status: status
    }
  end

  private

  def set_ends_at
    return unless scheduled_at && duration_minutes
    self.ends_at = scheduled_at + duration_minutes.minutes
  end

  def ends_at_after_scheduled_at
    return unless scheduled_at && ends_at
    errors.add(:ends_at, 'must be after scheduled time') if ends_at <= scheduled_at
  end
end
