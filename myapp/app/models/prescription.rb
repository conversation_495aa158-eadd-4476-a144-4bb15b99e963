class Prescription < ApplicationRecord
  belongs_to :consultation
  belongs_to :patient
  belongs_to :doctor

  # Enums
  enum :status, {
    active: 0,
    dispensed: 1,
    expired: 2,
    cancelled: 3
  }

  # Validations
  validates :prescription_number, presence: true, uniqueness: true
  validates :issued_at, presence: true
  validates :expires_at, presence: true
  validate :expires_at_after_issued_at
  validate :medications_present

  # Callbacks
  before_validation :generate_prescription_number, on: :create
  before_validation :set_issued_at, on: :create
  before_validation :set_expires_at, on: :create

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :expired, -> { where('expires_at < ?', Time.current) }
  scope :expiring_soon, -> { where(expires_at: Time.current..7.days.from_now) }
  scope :recent, -> { where('issued_at > ?', 30.days.ago) }

  # JSON columns don't need serialization in Rails 8

  # Instance methods
  def is_expired?
    expires_at < Time.current
  end

  def is_expiring_soon?
    expires_at < 7.days.from_now && expires_at > Time.current
  end

  def days_until_expiry
    return 0 if is_expired?
    ((expires_at - Time.current) / 1.day).ceil
  end

  def can_be_dispensed?
    active? && !is_expired?
  end

  def dispense!(pharmacy_info = {})
    return false unless can_be_dispensed?

    update!(
      status: :dispensed,
      dispensed_at: Time.current,
      pharmacy_name: pharmacy_info[:name],
      pharmacy_address: pharmacy_info[:address],
      dispensed_by: pharmacy_info[:dispensed_by]
    )
  end

  def medication_names
    return [] unless medications.is_a?(Array)
    medications.map { |med| med['name'] }.compact
  end

  def medication_count
    return 0 unless medications.is_a?(Array)
    medications.size
  end

  def prescription_summary
    {
      prescription_number: prescription_number,
      patient_name: patient.user.full_name,
      doctor_name: doctor.user.full_name,
      issued_date: issued_at.strftime('%Y-%m-%d'),
      expires_date: expires_at.strftime('%Y-%m-%d'),
      medication_count: medication_count,
      status: status
    }
  end

  def to_pdf_data
    {
      prescription_number: prescription_number,
      patient: {
        name: patient.user.full_name,
        medical_record_number: patient.medical_record_number,
        birth_date: patient.user.birth_date
      },
      doctor: {
        name: doctor.user.full_name,
        license_number: doctor.license_number,
        specialization: doctor.specialization
      },
      consultation: {
        date: consultation.started_at&.strftime('%Y-%m-%d %H:%M'),
        diagnosis: diagnosis
      },
      medications: medications,
      instructions: instructions,
      issued_at: issued_at,
      expires_at: expires_at
    }
  end

  private

  def generate_prescription_number
    return if prescription_number.present?

    loop do
      self.prescription_number = "RX#{Date.current.strftime('%Y%m%d')}#{SecureRandom.random_number(10000..99999)}"
      break unless Prescription.exists?(prescription_number: prescription_number)
    end
  end

  def set_issued_at
    self.issued_at ||= Time.current
  end

  def set_expires_at
    return if expires_at.present?
    # Default prescription validity is 30 days
    self.expires_at = (issued_at || Time.current) + 30.days
  end

  def expires_at_after_issued_at
    return unless issued_at && expires_at
    errors.add(:expires_at, 'must be after issued date') if expires_at <= issued_at
  end

  def medications_present
    return if medications.present? && medications.is_a?(Array) && medications.any?
    errors.add(:medications, 'must include at least one medication')
  end
end
