class CourseEnrollment < ApplicationRecord
  belongs_to :course
  belongs_to :patient

  # Enums
  enum :status, {
    active: 0,
    completed: 1,
    cancelled: 2,
    suspended: 3
  }

  # Validations
  validates :enrolled_at, presence: true
  validates :progress_percentage, numericality: { in: 0.0..100.0 }
  validates :rating, numericality: { in: 1.0..5.0 }, allow_nil: true
  validate :unique_active_enrollment

  # Callbacks
  after_create :increment_course_enrollment_count
  after_destroy :decrement_course_enrollment_count
  after_update :update_course_stats, if: :saved_change_to_rating?

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :completed, -> { where(status: :completed) }
  scope :with_reviews, -> { where.not(review: nil) }
  scope :recent, -> { where('enrolled_at > ?', 30.days.ago) }

  # Instance methods
  def is_completed?
    status == 'completed'
  end

  def can_be_completed?
    progress_percentage >= 100.0
  end

  def complete!
    return false unless can_be_completed?

    update!(
      status: :completed,
      completed_at: Time.current,
      progress_percentage: 100.0
    )
  end

  def update_progress!
    total_videos = course.course_videos.published.count
    return if total_videos == 0

    completed_videos = course.course_videos.published
                           .joins(:video_progresses)
                           .where(video_progresses: { patient: patient, completed: true })
                           .count

    new_progress = (completed_videos.to_f / total_videos * 100).round(2)
    
    update!(
      progress_percentage: new_progress,
      last_accessed_at: Time.current
    )

    complete! if new_progress >= 100.0 && !is_completed?
  end

  def time_spent_learning
    return 0 unless total_watch_time_seconds
    total_watch_time_seconds
  end

  def time_spent_formatted
    return '0m' if time_spent_learning == 0

    hours = time_spent_learning / 3600
    minutes = (time_spent_learning % 3600) / 60

    if hours > 0
      "#{hours}h #{minutes}m"
    else
      "#{minutes}m"
    end
  end

  def completion_percentage
    progress_percentage
  end

  def days_since_enrollment
    ((Time.current - enrolled_at) / 1.day).ceil
  end

  def estimated_completion_date
    return nil if progress_percentage == 0

    days_per_percent = days_since_enrollment.to_f / progress_percentage
    remaining_days = (100 - progress_percentage) * days_per_percent
    
    Date.current + remaining_days.days
  end

  def next_module
    course.next_module_for(patient)
  end

  def submit_review!(rating, review_text = nil)
    update!(
      rating: rating,
      review: review_text,
      review_submitted_at: Time.current
    )
  end

  def has_review?
    review.present?
  end

  def issue_certificate!
    return false unless is_completed?

    update!(
      certificate_issued: true,
      certificate_issued_at: Time.current
    )
  end

  def certificate_data
    return nil unless certificate_issued?

    {
      student_name: patient.user.full_name,
      course_title: course.title,
      instructor_name: course.instructor.user.full_name,
      completion_date: completed_at,
      certificate_issued_at: certificate_issued_at,
      course_duration: course.total_duration_minutes
    }
  end

  private

  def unique_active_enrollment
    return unless patient && course

    existing = CourseEnrollment.where(patient: patient, course: course)
                              .where.not(id: id)
                              .where(status: [:active, :completed])

    if existing.exists?
      errors.add(:base, 'Patient is already enrolled in this course')
    end
  end

  def increment_course_enrollment_count
    course.increment!(:enrollment_count)
  end

  def decrement_course_enrollment_count
    course.decrement!(:enrollment_count)
  end

  def update_course_stats
    course.update_stats!
  end
end
