class UsageTracker < ApplicationRecord
  belongs_to :user
  belongs_to :membership_feature

  # Validations
  validates :period_start, presence: true
  validates :period_end, presence: true
  validates :usage_count, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :limit_exceeded, inclusion: { in: [true, false] }
  validates :user_id, uniqueness: { scope: [:membership_feature_id, :period_start] }
  validate :period_end_after_start

  # Callbacks
  before_validation :set_default_period, on: :create
  before_save :check_limit_exceeded

  # Scopes
  scope :current_month, -> { where(period_start: Date.current.beginning_of_month) }
  scope :exceeded, -> { where(limit_exceeded: true) }
  scope :within_limit, -> { where(limit_exceeded: false) }
  scope :for_feature, ->(feature_key) { joins(:membership_feature).where(membership_features: { feature_key: feature_key }) }

  # Class methods
  def self.track_usage(user, feature_key, increment = 1)
    feature = MembershipFeature.find_by(feature_key: feature_key)
    return false unless feature

    period_start = Date.current.beginning_of_month
    period_end = Date.current.end_of_month

    tracker = find_or_initialize_by(
      user: user,
      membership_feature: feature,
      period_start: period_start
    )

    tracker.period_end = period_end
    tracker.usage_count = (tracker.usage_count || 0) + increment
    tracker.save!

    tracker
  end

  def self.current_usage(user, feature_key)
    current_month.for_feature(feature_key).find_by(user: user)&.usage_count || 0
  end

  def self.can_use_feature?(user, feature_key)
    current_usage = self.current_usage(user, feature_key)
    user_limit = user.current_membership&.membership_tier&.feature_limit(feature_key)

    return true if user_limit.nil? || user_limit == Float::INFINITY
    current_usage < user_limit
  end

  # Instance methods
  def current_limit
    user.current_membership&.membership_tier&.feature_limit(membership_feature.feature_key) || 0
  end

  def remaining_usage
    limit = current_limit
    return Float::INFINITY if limit == Float::INFINITY
    [limit - usage_count, 0].max
  end

  def usage_percentage
    limit = current_limit
    return 0 if limit == 0 || limit == Float::INFINITY
    ((usage_count.to_f / limit) * 100).round(2)
  end

  def within_limit?
    !limit_exceeded
  end

  private

  def period_end_after_start
    return unless period_start && period_end

    if period_end <= period_start
      errors.add(:period_end, "must be after period start")
    end
  end

  def set_default_period
    return if period_start.present? && period_end.present?

    self.period_start ||= Date.current.beginning_of_month
    self.period_end ||= Date.current.end_of_month
  end

  def check_limit_exceeded
    limit = current_limit
    self.limit_exceeded = limit != Float::INFINITY && usage_count >= limit
  end
end
