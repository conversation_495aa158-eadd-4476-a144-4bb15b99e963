class Api::V1::Doctors<PERSON>ontroller < Api::V1::BaseController
  skip_before_action :authenticate_user!, only: [:show, :availability]
  before_action :set_doctor, only: [:show, :update, :profile, :update_profile, :availability, :update_availability, :statistics]
  before_action :ensure_doctor_access, except: [:show, :availability]

  def show
    render_success({
      doctor: doctor_public_data(@doctor),
      availability: availability_data(@doctor)
    })
  end

  def update
    if @doctor.update(doctor_params)
      render_success({
        doctor: doctor_data(@doctor),
        message: 'Doctor profile updated successfully'
      })
    else
      render_error('Failed to update doctor profile', errors: @doctor.errors.full_messages, status: :unprocessable_entity)
    end
  end

  def profile
    render_success({
      doctor: doctor_data(@doctor),
      user: user_data(@doctor.user),
      verification: verification_data(@doctor.doctor_verification)
    })
  end

  def update_profile
    if @doctor.update(doctor_params)
      render_success({
        doctor: doctor_data(@doctor),
        message: 'Doctor profile updated successfully'
      })
    else
      render_error('Failed to update doctor profile', errors: @doctor.errors.full_messages, status: :unprocessable_entity)
    end
  end

  def availability
    render_success({
      availability: @doctor.doctor_availabilities.map { |avail| availability_detail_data(avail) },
      available_slots: generate_available_slots(@doctor)
    })
  end

  def update_availability
    # This would handle bulk update of availability
    # For now, just return current availability
    render_success({
      availability: @doctor.doctor_availabilities.map { |avail| availability_detail_data(avail) },
      message: 'Availability updated successfully'
    })
  end

  def statistics
    render_success({
      statistics: {
        total_appointments: @doctor.appointments.count,
        completed_appointments: @doctor.appointments.completed.count,
        upcoming_appointments: @doctor.appointments.upcoming.count,
        total_consultations: @doctor.total_consultations,
        average_rating: @doctor.average_rating,
        this_month_appointments: @doctor.appointments.where(scheduled_at: Date.current.beginning_of_month..Date.current.end_of_month).count,
        this_week_appointments: @doctor.appointments.this_week.count,
        today_appointments: @doctor.appointments.today.count
      }
    })
  end

  private

  def set_doctor
    @doctor = Doctor.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render_not_found('Doctor not found')
  end

  def ensure_doctor_access
    unless current_user.doctor && current_user.doctor.id == @doctor.id
      render_forbidden('You can only access your own doctor profile')
    end
  end

  def doctor_params
    params.permit(
      :specialization, :graduated_university, :graduation_year,
      :current_institution, :years_experience, :consultation_fee_day,
      :consultation_fee_night, :consultation_fee_emergency, :bio,
      sub_specializations: [], languages_spoken: []
    )
  end

  def doctor_public_data(doctor)
    {
      id: doctor.id,
      name: doctor.user.full_name,
      first_name: doctor.user.first_name,
      last_name: doctor.user.last_name,
      specialization: doctor.specialization,
      sub_specializations: doctor.sub_specializations,
      years_experience: doctor.years_experience,
      consultation_fee_day: doctor.consultation_fee_day,
      consultation_fee_night: doctor.consultation_fee_night,
      consultation_fee_emergency: doctor.consultation_fee_emergency,
      rating: doctor.rating || 0.0,
      total_consultations: doctor.total_consultations,
      languages_spoken: doctor.languages_spoken,
      bio: doctor.bio,
      graduated_university: doctor.graduated_university,
      graduation_year: doctor.graduation_year,
      current_institution: doctor.current_institution,
      verification_status: doctor.verification_status,
      available_today: doctor.available_today?
    }
  end

  def doctor_data(doctor)
    doctor_public_data(doctor).merge({
      license_number: doctor.license_number,
      user_id: doctor.user_id,
      created_at: doctor.created_at.iso8601,
      updated_at: doctor.updated_at.iso8601
    })
  end

  def user_data(user)
    {
      id: user.id,
      email: user.email,
      phone_number: user.phone_number,
      birth_date: user.birth_date,
      locale: user.locale,
      timezone: user.timezone,
      verified_at: user.verified_at
    }
  end

  def verification_data(verification)
    return nil unless verification
    
    {
      id: verification.id,
      status: verification.status,
      submitted_at: verification.submitted_at,
      reviewed_at: verification.reviewed_at,
      notes: verification.notes
    }
  end

  def availability_data(doctor)
    doctor.doctor_availabilities.map do |availability|
      {
        day_of_week: availability.day_of_week,
        day_name: Date::DAYNAMES[availability.day_of_week],
        is_available: availability.is_available,
        start_time: availability.start_time,
        end_time: availability.end_time
      }
    end
  end

  def availability_detail_data(availability)
    {
      id: availability.id,
      day_of_week: availability.day_of_week,
      day_name: Date::DAYNAMES[availability.day_of_week],
      is_available: availability.is_available,
      start_time: availability.start_time,
      end_time: availability.end_time,
      appointment_duration: availability.appointment_duration,
      buffer_time: availability.buffer_time,
      max_appointments: availability.max_appointments,
      created_at: availability.created_at.iso8601,
      updated_at: availability.updated_at.iso8601
    }
  end

  def generate_available_slots(doctor)
    # Generate available slots for the next 7 days
    slots = []
    (0..6).each do |day_offset|
      date = Date.current + day_offset.days
      day_of_week = date.wday
      availability = doctor.doctor_availabilities.find_by(day_of_week: day_of_week)

      if availability&.is_available?
        # Generate time slots for this day (simplified)
        start_time = date.beginning_of_day + 9.hours # 9 AM
        end_time = date.beginning_of_day + 17.hours # 5 PM
        slot_duration = 30.minutes

        current_time = start_time
        while current_time < end_time
          # Check if slot is not already booked
          unless doctor.appointments.where(scheduled_at: current_time).exists?
            slots << {
              scheduled_at: current_time.iso8601,
              date: date.iso8601,
              time: current_time.strftime('%H:%M'),
              available: true,
              consultation_fee: doctor.consultation_fee_for_time(:day)
            }
          end
          current_time += slot_duration
        end
      end
    end

    slots
  end
end
