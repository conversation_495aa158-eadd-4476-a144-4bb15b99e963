class Api::V1::AppointmentsController < Api::V1::BaseController
  skip_before_action :authenticate_user!, only: [:available_slots]
  before_action :set_appointment, only: [:show, :update]
  before_action :ensure_patient_or_doctor, only: [:show, :update]

  def index
    # This method handles general appointment listing
    # For patient-specific appointments, use the patients controller
    appointments = Appointment.includes(:patient => :user, :doctor => :user)

    # Filter by current user if they are a patient or doctor
    if current_user.patient
      appointments = appointments.where(patient: current_user.patient)
    elsif current_user.doctor
      appointments = appointments.where(doctor: current_user.doctor)
    else
      return render_forbidden('Access denied')
    end

    # Apply filters
    appointments = appointments.where(status: params[:status]) if params[:status].present?

    total_count = appointments.count
    per_page = params[:per_page]&.to_i || 20
    paginated_appointments = paginate_collection(appointments.order(scheduled_at: :desc), per_page: per_page)

    render_success({
      appointments: paginated_appointments.map { |appointment| appointment_data(appointment) },
      pagination: pagination_meta(paginated_appointments, total_count, per_page: per_page)
    })
  end

  def create
    # Ensure user is a patient
    patient = current_user.patient
    return render_error('Only patients can book appointments', status: :forbidden) unless patient

    doctor = Doctor.find(params[:doctor_id])
    return render_error('Doctor not found', status: :not_found) unless doctor
    return render_error('Doctor is not verified', status: :unprocessable_entity) unless doctor.verified?

    appointment_params = {
      patient: patient,
      doctor: doctor,
      scheduled_at: Time.parse(params[:scheduled_at]),
      duration_minutes: params[:duration_minutes] || 30,
      appointment_type: params[:appointment_type] || 'consultation',
      patient_notes: params[:patient_notes]
    }

    appointment = Appointment.new(appointment_params)

    if appointment.save
      # Here you would typically send notifications to the doctor
      render_success({
        appointment: appointment_data(appointment),
        message: 'Appointment booked successfully'
      })
    else
      render_error('Failed to book appointment', errors: appointment.errors.full_messages, status: :unprocessable_entity)
    end
  end

  def show
    render_success({ appointment: appointment_data(@appointment) })
  end

  def update
    if @appointment.update(update_appointment_params)
      render_success({
        appointment: appointment_data(@appointment),
        message: 'Appointment updated successfully'
      })
    else
      render_error('Failed to update appointment', errors: @appointment.errors.full_messages, status: :unprocessable_entity)
    end
  end

  def available_slots
    doctor = Doctor.find(params[:doctor_id]) if params[:doctor_id]
    return render_error('Doctor not found', status: :not_found) unless doctor

    date = Date.parse(params[:date]) if params[:date]
    date ||= Date.current

    # Get doctor's availability for the requested date
    day_of_week = date.wday
    availability = doctor.doctor_availabilities.find_by(day_of_week: day_of_week)
    
    if availability&.is_available?
      # Generate available time slots (simplified logic)
      slots = generate_time_slots(doctor, date, availability)
      render_success({
        available_slots: slots,
        date: date,
        doctor_id: doctor.id
      })
    else
      render_success({
        available_slots: [],
        message: 'Doctor is not available on this day',
        date: date,
        doctor_id: doctor.id
      })
    end
  end

  private

  def set_appointment
    @appointment = Appointment.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render_not_found('Appointment not found')
  end

  def ensure_patient_or_doctor
    patient = current_user.patient
    doctor = current_user.doctor
    
    unless (patient && @appointment.patient == patient) || (doctor && @appointment.doctor == doctor)
      render_forbidden('You can only view your own appointments')
    end
  end

  def update_appointment_params
    allowed_params = []
    
    # Patients can update notes and cancel
    if current_user.patient
      allowed_params += [:patient_notes, :status]
    end
    
    # Doctors can update status and add doctor notes
    if current_user.doctor
      allowed_params += [:status, :doctor_notes]
    end
    
    params.permit(allowed_params)
  end

  def appointment_data(appointment)
    {
      id: appointment.id.to_s,
      patient_id: appointment.patient_id,
      doctor_id: appointment.doctor_id,
      scheduled_at: appointment.scheduled_at.iso8601,
      ends_at: appointment.ends_at.iso8601,
      duration_minutes: appointment.duration_minutes,
      appointment_type: appointment.appointment_type,
      status: appointment.status,
      consultation_fee: appointment.consultation_fee.to_f,
      patient_notes: appointment.patient_notes,
      doctor_notes: appointment.doctor_notes,
      created_at: appointment.created_at.iso8601,
      updated_at: appointment.updated_at.iso8601,
      # Additional fields expected by tests
      doctor_name: appointment.doctor.user.full_name,
      appointment_date: appointment.scheduled_at.to_date.iso8601,
      appointment_time: appointment.scheduled_at.strftime('%H:%M'),
      patient: {
        id: appointment.patient.id,
        name: appointment.patient.user.full_name,
        medical_record_number: appointment.patient.medical_record_number
      },
      doctor: {
        id: appointment.doctor.id,
        name: appointment.doctor.user.full_name,
        specialization: appointment.doctor.specialization,
        consultation_fee: appointment.doctor.consultation_fee_day.to_f
      }
    }
  end

  def generate_time_slots(doctor, date, availability)
    # Simplified slot generation - in production you'd want more sophisticated logic
    slots = []
    start_time = date.beginning_of_day + 9.hours # 9 AM
    end_time = date.beginning_of_day + 17.hours # 5 PM
    slot_duration = 30.minutes

    current_time = start_time
    while current_time < end_time
      # Check if slot is not already booked
      unless doctor.appointments.where(scheduled_at: current_time).exists?
        slots << {
          scheduled_at: current_time.iso8601,
          available: true,
          consultation_fee: doctor.consultation_fee_for_time(:day)
        }
      end
      current_time += slot_duration
    end

    slots
  end
end
