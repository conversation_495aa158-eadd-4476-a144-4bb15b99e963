require 'jwt'

class Api::V1::BaseController < ApplicationController
  before_action :authenticate_user!, except: [:index, :show] # Allow public access to some endpoints
  before_action :set_current_user
  
  rescue_from ActiveRecord::RecordNotFound, with: :render_not_found
  rescue_from ActiveRecord::RecordInvalid, with: :render_unprocessable_entity
  rescue_from JWT::DecodeError, with: :render_unauthorized
  rescue_from JWT::ExpiredSignature, with: :render_unauthorized

  protected

  def authenticate_user!
    render_unauthorized unless current_user
  end

  def current_user
    @current_user
  end

  def set_current_user
    token = extract_token_from_header
    return unless token

    begin
      decoded_token = JWT.decode(token, Rails.application.secret_key_base, true, { algorithm: 'HS256' })
      user_id = decoded_token[0]['user_id']
      @current_user = User.find(user_id)
    rescue JWT::DecodeError, JWT::ExpiredSignature, ActiveRecord::RecordNotFound
      @current_user = nil
    end
  end

  def extract_token_from_header
    auth_header = request.headers['Authorization']
    return unless auth_header&.start_with?('Bearer ')
    
    auth_header.split(' ').last
  end

  def generate_jwt_token(user)
    payload = {
      user_id: user.id,
      exp: 24.hours.from_now.to_i
    }
    JWT.encode(payload, Rails.application.secret_key_base)
  end

  # Response helpers
  def render_success(data = {}, status: :ok)
    render json: { success: true, **data }, status: status
  end

  def render_error(message, status: :bad_request, errors: nil)
    response = { success: false, error: message }
    response[:errors] = errors if errors
    render json: response, status: status
  end

  def render_unauthorized(message = 'Authentication required')
    render json: { success: false, error: message }, status: :unauthorized
  end

  def render_forbidden(message = 'Access denied')
    render json: { success: false, error: message }, status: :forbidden
  end

  def render_not_found(exception = nil)
    message = exception&.message || 'Resource not found'
    render json: { success: false, error: message }, status: :not_found
  end

  def render_unprocessable_entity(exception)
    render json: { 
      success: false, 
      error: 'Validation failed', 
      errors: exception.record.errors.full_messages 
    }, status: :unprocessable_entity
  end

  # Pagination helpers
  def paginate_collection(collection, per_page: 20)
    page = params[:page]&.to_i || 1
    per_page = [per_page, 100].min # Max 100 items per page
    
    collection.page(page).per(per_page)
  end

  def pagination_meta(collection)
    {
      current_page: collection.current_page,
      per_page: collection.limit_value,
      total_pages: collection.total_pages,
      total_count: collection.total_count
    }
  end
end
